# Chat with DB - AI-Powered Multi-Database Chat Interface

A full-stack TypeScript application that allows users to interact with multiple database types (MongoDB, PostgreSQL, MySQL) through natural language using a locally hosted Ollama AI model.

## ✨ Features

- 🤖 **Natural Language Queries**: Ask questions in plain English, get database results
- 🗄️ **Multi-Database Support**: MongoDB, PostgreSQL, and MySQL
- 💬 **Clean Interface**: Responsive chat interface built with React
- 🔧 **Configuration-Based**: Easy database management through JSON config
- 🎯 **Database-Specific Prompts**: Optimized AI prompts for each database type
- 🔍 **Auto Schema Detection**: Automatic database schema inspection
- 📊 **Result Visualization**: Clean display of query results
- 🚀 **Single Command Startup**: Both frontend and backend with one command
- 🔧 **Full TypeScript**: Type safety throughout the stack
- 🏗️ **Modern Architecture**: Clean, extensible, and maintainable codebase

## 📋 Prerequisites

Before running this application, ensure you have:

1. **Node.js** (v18 or higher)
2. **At least one database** running:
   - **MongoDB** (port 27017) - ✅ enabled by default
   - **PostgreSQL** (port 5432) - ⚙️ configurable
   - **MySQL** (port 3306) - ⚙️ configurable
3. **Ollama** installed and running locally with a model

### 🤖 Installing Ollama

1. Install Ollama from [https://ollama.ai](https://ollama.ai)
2. Pull a model: `ollama pull llama2` or `ollama pull devstral:latest`
3. Verify it's running: `ollama list`

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start both frontend and backend
npm run dev
```

The application will be available at `http://localhost:3002`

## 🎯 How to Use

1. **Open** the application in your browser
2. **Select Database**: Use the database selector to choose:
   - Database type (MongoDB, PostgreSQL, MySQL)
   - Database name
   - Table/Collection name
3. **Ask Questions**: Type natural language questions about your data
4. **Get Results**: View formatted results and generated queries

## 🔌 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/chat` | Send natural language query with database selection |
| `GET` | `/api/health` | Check service status for all configured databases |
| `GET` | `/api/databases` | List available database configurations |
| `GET` | `/api/tables` | List available tables/collections for a database |
| `GET` | `/api/collections` | List available collections (backward compatibility) |

### Chat Request Format

```json
{
  "message": "How many users do we have?",
  "databaseId": "mongodb",
  "database": "myapp",
  "collection": "users"
}
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
# Backend Configuration
PORT=3002
MONGODB_URI=mongodb://localhost:27017
OLLAMA_MODEL=devstral:latest
NODE_ENV=development

# Frontend Configuration
VITE_API_URL=http://localhost:3002/api
```

### Database Configuration

Edit `backend/src/config/database-config.json` to configure your databases:

```json
{
  "databases": {
    "mongodb": {
      "id": "mongodb",
      "name": "MongoDB",
      "type": "mongodb",
      "enabled": true,
      "connection": {
        "uri": "mongodb://localhost:27017"
      },
      "defaultDatabase": "test",
      "defaultCollection": "users"
    },
    "postgresql": {
      "id": "postgresql",
      "name": "PostgreSQL",
      "type": "postgresql",
      "enabled": false,
      "connection": {
        "host": "localhost",
        "port": 5432,
        "database": "testdb",
        "username": "postgres",
        "password": "password"
      }
    },
    "mysql": {
      "id": "mysql",
      "name": "MySQL",
      "type": "mysql",
      "enabled": false,
      "connection": {
        "host": "localhost",
        "port": 3306,
        "database": "testdb",
        "username": "root",
        "password": "password"
      }
    }
  }
}
```

**To enable additional databases**: Set `"enabled": true` and update connection details.

## 💬 Example Queries

Try asking questions like:

**General Queries:**
- "How many users do we have?"
- "Show me users created this month"
- "What are the most common user ages?"
- "Find users with email containing 'gmail'"

**Analytics Queries:**
- "What's the average order value?"
- "Show me the top 10 products by sales"
- "How many orders were placed yesterday?"
- "What's the most popular category?"

**Complex Queries:**
- "Show me users who haven't logged in for 30 days"
- "What's the revenue trend for the last 6 months?"
- "Find customers with more than 5 orders"

## 🛠️ Development

### Project Structure

```
chatWithDb/
├── backend/                 # Express.js API server
│   ├── src/
│   │   ├── config/         # Database and prompt configurations
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic and database services
│   │   └── types/          # TypeScript definitions
│   └── package.json
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API communication
│   │   └── types/          # TypeScript definitions
│   └── package.json
└── package.json           # Root workspace configuration
```

### Running Individual Services

```bash
# Backend only
npm run dev:backend

# Frontend only
npm run dev:frontend
```

### Building for Production

```bash
# Build both frontend and backend
npm run build

# Start production server
npm start
```

### Adding New Database Types

1. Create a new service class implementing `DatabaseService`
2. Add the database type to `DatabaseFactory`
3. Add configuration schema to `database-config.json`
4. Add prompts to `prompts.json`
5. Update TypeScript types as needed

## 🐛 Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| **Ollama not responding** | Ensure Ollama is running: `ollama serve` |
| **Database connection failed** | Check database server status and connection strings |
| **Port already in use** | Change the PORT in `.env` file |
| **Model not found** | Pull the required model: `ollama pull <model-name>` |
| **TypeScript errors** | Run `npm run type-check` in backend/frontend |

### Debug Mode

Set `NODE_ENV=development` in your `.env` file for detailed error messages.

### Logs

Check the console output for detailed error messages and debugging information.

## 🏗️ Architecture

The application uses a modern, extensible architecture:

- **Configuration-Based**: Database connections managed through JSON config
- **Abstract Database Layer**: Common interface for all database types
- **Factory Pattern**: Dynamic database service creation
- **Database-Specific Prompts**: Optimized AI prompts for each database type
- **Type Safety**: Full TypeScript implementation
- **Modular Design**: Easy to extend with new database types

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

### Development Guidelines

1. Follow TypeScript best practices
2. Add tests for new features
3. Update documentation as needed
4. Follow the existing code style

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [Ollama](https://ollama.ai) for local AI model hosting
- [LangChain.js](https://js.langchain.com) for AI integration
- [React](https://react.dev) for the frontend framework
- [Express.js](https://expressjs.com) for the backend framework
