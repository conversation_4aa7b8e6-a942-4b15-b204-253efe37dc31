#!/usr/bin/env node

const axios = require('axios');

const API_URL = 'http://localhost:3001/api/chat';

async function testSimpleCollection() {
  console.log('🧪 Testing Simplified Collection Auto-Detection\n');

  const testCases = [
    {
      name: 'User query',
      request: {
        message: 'How many users do we have?',
        databaseId: 'mongodb',
        database: 'test'
      },
      description: 'Should auto-detect collection from "users" keyword'
    },
    {
      name: 'Generic query',
      request: {
        message: 'Show me some data',
        databaseId: 'mongodb',
        database: 'test'
      },
      description: 'Should use first available collection'
    },
    {
      name: 'Product query',
      request: {
        message: 'List all products',
        databaseId: 'mongodb',
        database: 'test'
      },
      description: 'Should try to detect products collection'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 Test: ${testCase.name}`);
    console.log(`   Description: ${testCase.description}`);
    console.log(`   Request: ${JSON.stringify(testCase.request, null, 2)}`);
    
    try {
      const response = await axios.post(API_URL, testCase.request, {
        timeout: 30000
      });
      
      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   📊 Response: ${response.data.response.substring(0, 150)}...`);
      
      if (response.data.detectionInfo) {
        console.log(`   🔍 Detection: ${response.data.detectionInfo}`);
      }
      
      if (response.data.usedTable) {
        console.log(`   🎯 Used Collection: ${response.data.usedTable}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.error || error.message}`);
    }
    
    console.log('   ' + '─'.repeat(60));
  }
}

async function main() {
  console.log('🚀 Simple Collection Detection Test\n');
  
  try {
    const health = await axios.get('http://localhost:3001/api/health');
    console.log('✅ Server is running');
    console.log(`📊 Ollama: ${health.data.services.ollama}`);
  } catch (error) {
    console.log('❌ Server is not running. Please start it with: npm run dev:backend');
    return;
  }
  
  await testSimpleCollection();
  
  console.log('\n✨ Test completed!');
  console.log('\n💡 Features:');
  console.log('   • Collections are automatically detected from user queries');
  console.log('   • No complex auto-detection toggles');
  console.log('   • Simple and straightforward');
}

main().catch(console.error);
