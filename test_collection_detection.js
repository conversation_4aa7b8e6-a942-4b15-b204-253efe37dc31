#!/usr/bin/env node

const axios = require('axios');

const API_URL = 'http://localhost:3001/api/chat';

async function testCollectionDetection() {
  console.log('🧪 Testing Collection Auto-Detection Feature\n');

  const testCases = [
    {
      name: 'User-related query',
      request: {
        message: 'How many users do we have?',
        autoDetect: true
      },
      description: 'Should auto-detect "users" collection'
    },
    {
      name: 'Product-related query',
      request: {
        message: 'Show me all products',
        autoDetect: true
      },
      description: 'Should auto-detect "products" collection if available'
    },
    {
      name: 'Order-related query',
      request: {
        message: 'Count all orders from this month',
        autoDetect: true
      },
      description: 'Should auto-detect "orders" collection if available'
    },
    {
      name: 'Generic query',
      request: {
        message: 'Show me some data',
        autoDetect: true
      },
      description: 'Should fall back to first available collection'
    },
    {
      name: 'Manual database with auto collection',
      request: {
        message: 'Tell me about customers',
        autoDetect: false,
        databaseId: 'mongodb',
        database: 'test'
      },
      description: 'Should auto-detect collection even with manual database selection'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 Test: ${testCase.name}`);
    console.log(`   Description: ${testCase.description}`);
    console.log(`   Request: ${JSON.stringify(testCase.request, null, 2)}`);
    
    try {
      const response = await axios.post(API_URL, testCase.request, {
        timeout: 45000
      });
      
      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   📊 Response: ${response.data.response.substring(0, 150)}...`);
      
      if (response.data.detectionInfo) {
        console.log(`   🔍 Detection: ${response.data.detectionInfo}`);
      }
      
      if (response.data.usedDatabaseId) {
        console.log(`   🎯 Used: ${response.data.usedDatabaseId}.${response.data.usedDatabase}.${response.data.usedTable}`);
      }
      
      if (response.data.query) {
        console.log(`   🔧 Query: ${JSON.stringify(response.data.query)}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.error || error.message}`);
      if (error.response?.data?.detectionInfo) {
        console.log(`   🔍 Detection: ${error.response.data.detectionInfo}`);
      }
    }
    
    console.log('   ' + '─'.repeat(80));
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await axios.get('http://localhost:3001/api/health');
    console.log('✅ Server is running');
    console.log(`📊 Ollama: ${response.data.services.ollama}`);
    console.log(`🗄️  Databases: ${JSON.stringify(response.data.services.databases)}`);
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Please start it with: npm run dev:backend');
    return false;
  }
}

async function main() {
  console.log('🚀 Collection Auto-Detection Test\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    process.exit(1);
  }
  
  await testCollectionDetection();
  
  console.log('\n✨ Test completed!');
  console.log('\n💡 Key Features Tested:');
  console.log('   • Automatic collection detection from user queries');
  console.log('   • No manual collection selection required');
  console.log('   • AI analyzes query context to choose best collection');
  console.log('   • Fallback to available collections when detection is unclear');
}

if (require.main === module) {
  main().catch(console.error);
}
