{"name": "chat-with-db", "version": "1.0.0", "description": "AI-powered chat application for MongoDB database", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "npm run build:frontend && npm run dev:backend", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "dev:separate": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "npm run build --workspace=frontend", "build:backend": "npm run build --workspace=backend", "start": "npm run build && npm run start --workspace=backend", "install:all": "npm install && npm install --workspace=backend && npm install --workspace=frontend"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}}