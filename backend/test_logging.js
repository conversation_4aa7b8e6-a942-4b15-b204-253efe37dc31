const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testLogging() {
  console.log('🧪 Testing Comprehensive Logging Framework\n');

  try {
    // Test 1: Health check endpoint
    console.log('1. Testing health check endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log(`✅ Health check: ${healthResponse.data.status}`);
    console.log(`   Duration: ${healthResponse.data.duration}ms`);
    
    // Test 2: Chat endpoint with valid request
    console.log('\n2. Testing chat endpoint with valid request...');
    const chatResponse = await axios.post(`${BASE_URL}/api/chat`, {
      message: "How many users are there?",
      database: "test",
      databaseId: "mongodb"
    });
    console.log(`✅ Chat response received`);
    console.log(`   Response length: ${chatResponse.data.response.length} characters`);
    
    // Test 3: Chat endpoint with invalid request (missing message)
    console.log('\n3. Testing chat endpoint with invalid request...');
    try {
      await axios.post(`${BASE_URL}/api/chat`, {
        database: "test",
        databaseId: "mongodb"
      });
    } catch (error) {
      console.log(`✅ Expected error caught: ${error.response.status} - ${error.response.data.error}`);
    }
    
    // Test 4: Chat endpoint with general conversation
    console.log('\n4. Testing chat endpoint with general conversation...');
    const generalResponse = await axios.post(`${BASE_URL}/api/chat`, {
      message: "Hello, how are you?",
      database: "test",
      databaseId: "mongodb"
    });
    console.log(`✅ General conversation response received`);
    console.log(`   Response: ${generalResponse.data.response.substring(0, 100)}...`);
    
    // Test 5: Database listing endpoint
    console.log('\n5. Testing databases endpoint...');
    const dbResponse = await axios.get(`${BASE_URL}/api/databases`);
    console.log(`✅ Databases listed: ${dbResponse.data.databases.length} databases`);
    
    // Test 6: Simulate slow request (if any endpoint takes time)
    console.log('\n6. Testing performance logging with multiple requests...');
    const promises = [];
    for (let i = 0; i < 3; i++) {
      promises.push(
        axios.post(`${BASE_URL}/api/chat`, {
          message: `Tell me about user ${i + 1}`,
          database: "test",
          databaseId: "mongodb"
        })
      );
    }
    
    const results = await Promise.all(promises);
    console.log(`✅ Concurrent requests completed: ${results.length} responses`);
    
    console.log('\n🎉 All logging tests completed successfully!');
    console.log('\n📝 Check the following for logs:');
    console.log('   • Console output (you should see structured logs)');
    console.log('   • backend/logs/ directory for log files');
    console.log('   • Look for request IDs, operation tracking, and performance metrics');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

async function checkServerStatus() {
  try {
    await axios.get(`${BASE_URL}/api/health`);
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🚀 Comprehensive Logging Test Suite\n');
  
  const isServerRunning = await checkServerStatus();
  if (!isServerRunning) {
    console.log('❌ Server is not running. Please start it with: npm run dev:backend');
    console.log('   Then run this test again to see the logging in action.');
    return;
  }
  
  await testLogging();
  
  console.log('\n💡 Logging Features Tested:');
  console.log('   ✓ Request/Response logging with correlation IDs');
  console.log('   ✓ Performance monitoring and slow request detection');
  console.log('   ✓ Database operation logging');
  console.log('   ✓ AI/LLM operation tracking');
  console.log('   ✓ Error logging with stack traces');
  console.log('   ✓ Health check monitoring');
  console.log('   ✓ Configuration loading logs');
  console.log('   ✓ Structured JSON logging for production');
  console.log('   ✓ Colored console logs for development');
  console.log('   ✓ Log rotation and file management');
}

main().catch(console.error);
