{"databases": {"mongodb": {"id": "mongodb", "name": "MongoDB", "type": "mongodb", "enabled": true, "connection": {"uri": "mongodb://localhost:27017", "options": {"maxPoolSize": 10, "serverSelectionTimeoutMS": 5000}}, "defaultDatabase": "test", "defaultCollection": "users"}, "postgresql": {"id": "postgresql", "name": "PostgreSQL", "type": "postgresql", "enabled": false, "connection": {"host": "localhost", "port": 5432, "database": "testdb", "username": "postgres", "password": "password", "options": {"ssl": false, "connectionTimeoutMillis": 5000}}, "defaultSchema": "public"}, "mysql": {"id": "mysql", "name": "MySQL", "type": "mysql", "enabled": false, "connection": {"host": "localhost", "port": 3306, "database": "testdb", "username": "root", "password": "password", "options": {"connectionLimit": 10, "acquireTimeout": 60000, "timeout": 60000}}, "defaultSchema": "testdb"}}}