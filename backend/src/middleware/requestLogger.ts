import { Request, Response, NextFunction } from 'express';
import { logger } from '../services/Logger';
import { v4 as uuidv4 } from 'uuid';

// Extend Express Request interface to include requestId and startTime
declare global {
  namespace Express {
    interface Request {
      requestId: string;
      startTime: number;
    }
  }
}

export interface RequestLoggerOptions {
  includeBody?: boolean;
  includeHeaders?: boolean;
  excludePaths?: string[];
  maxBodyLength?: number;
}

export function requestLogger(options: RequestLoggerOptions = {}) {
  const {
    includeBody = false,
    includeHeaders = false,
    excludePaths = ['/health', '/favicon.ico'],
    maxBodyLength = 1000
  } = options;

  return (req: Request, res: Response, next: NextFunction) => {
    // Skip logging for excluded paths
    if (excludePaths.some(path => req.path.includes(path))) {
      return next();
    }

    // Generate unique request ID
    req.requestId = req.headers['x-request-id'] as string || uuidv4();
    req.startTime = Date.now();

    // Log request start
    const requestContext: any = {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl || req.url,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
      operation: 'http_request'
    };

    if (includeHeaders) {
      requestContext.headers = sanitizeHeaders(req.headers);
    }

    if (includeBody && req.body) {
      requestContext.body = sanitizeBody(req.body, maxBodyLength);
    }

    logger.api('Incoming request', requestContext);

    // Capture original res.end to log response
    const originalEnd = res.end;
    let responseBody: any;

    // Capture response body if it's JSON
    const originalJson = res.json;
    res.json = function(body: any) {
      responseBody = body;
      return originalJson.call(this, body);
    };

    res.end = function(chunk?: any, encoding?: any) {
      const duration = Date.now() - req.startTime;
      
      const responseContext: any = {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl || req.url,
        statusCode: res.statusCode,
        duration,
        operation: 'http_response'
      };

      // Add response body if available and not too large
      if (responseBody && includeBody) {
        responseContext.responseBody = sanitizeBody(responseBody, maxBodyLength);
      }

      // Log based on status code
      if (res.statusCode >= 500) {
        logger.error('Request failed with server error', responseContext);
      } else if (res.statusCode >= 400) {
        logger.warn('Request failed with client error', responseContext);
      } else {
        logger.api('Request completed successfully', responseContext);
      }

      // Performance logging for slow requests
      if (duration > 1000) {
        logger.performance('Slow request detected', {
          requestId: req.requestId,
          method: req.method,
          url: req.originalUrl || req.url,
          duration,
          operation: 'slow_request'
        });
      }

      return originalEnd.call(this, chunk, encoding);
    };

    next();
  };
}

// Error logging middleware
export function errorLogger() {
  return (err: Error, req: Request, res: Response, next: NextFunction) => {
    const duration = req.startTime ? Date.now() - req.startTime : undefined;
    
    logger.error('Request error', {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl || req.url,
      statusCode: res.statusCode,
      duration,
      operation: 'http_error'
    }, err);

    next(err);
  };
}

// Utility functions
function sanitizeHeaders(headers: any): any {
  const sanitized = { ...headers };
  
  // Remove sensitive headers
  const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
  sensitiveHeaders.forEach(header => {
    if (sanitized[header]) {
      sanitized[header] = '[REDACTED]';
    }
  });

  return sanitized;
}

function sanitizeBody(body: any, maxLength: number): any {
  if (!body) return body;

  let sanitized = { ...body };

  // Remove sensitive fields
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });

  // Truncate if too long
  const bodyStr = JSON.stringify(sanitized);
  if (bodyStr.length > maxLength) {
    return {
      ...sanitized,
      _truncated: true,
      _originalLength: bodyStr.length
    };
  }

  return sanitized;
}

// Health check middleware that doesn't log
export function healthCheckLogger() {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.path === '/health' || req.path === '/api/health') {
      // Only log health check failures
      const originalEnd = res.end;
      res.end = function(chunk?: any, encoding?: any) {
        if (res.statusCode >= 400) {
          logger.warn('Health check failed', {
            statusCode: res.statusCode,
            operation: 'health_check_failed'
          });
        }
        return originalEnd.call(this, chunk, encoding);
      };
    }
    next();
  };
}
