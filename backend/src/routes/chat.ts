import { Router, Request, Response } from 'express';
import { DatabaseLangChainService } from '../services/DatabaseLangChainService';
import { DatabaseManager } from '../services/DatabaseManager';
import { ConfigManager } from '../services/ConfigManager';
import { ChatRequest, ChatResponse, DatabaseInfo } from '../types';
import { logger } from '../services/Logger';

const router = Router();

// Initialize services
const databaseManager = DatabaseManager.getInstance();
const configManager = ConfigManager.getInstance();

logger.info('Chat routes initialized', {
  ollamaModel: process.env.OLLAMA_MODEL,
  enabledDatabases: databaseManager.getEnabledDatabases().map(db => db.id),
  operation: 'routes_init'
});

router.post('/chat', async (req: Request, res: Response) => {
  const startTime = Date.now();
  const requestId = req.requestId;

  try {
    const {
      message,
      database = 'test',
      databaseId = 'mongodb',
      databaseType
    }: ChatRequest = req.body;

    logger.info('Chat request received', {
      requestId,
      messageLength: message?.length || 0,
      databaseId,
      database,
      operation: 'chat_start'
    });

    if (!message) {
      logger.warn('Chat request missing message', { requestId });
      return res.status(400).json({
        error: 'Message is required',
        requestId
      } as ChatResponse);
    }

    // Get the database service
    logger.database('Getting database service', {
      requestId,
      databaseId,
      operation: 'get_database_service'
    });

    const databaseService = databaseManager.getDatabaseService(databaseId);
    const actualDatabaseType = databaseService.getType();

    // Create LangChain service for this database
    logger.info('Creating LangChain service', {
      requestId,
      databaseId,
      databaseType: actualDatabaseType,
      model: process.env.OLLAMA_MODEL || 'llama2',
      operation: 'create_langchain_service'
    });

    const langchainService = new DatabaseLangChainService(
      databaseService,
      process.env.OLLAMA_MODEL || 'llama2'
    );

    // Process the user's question (collection will be auto-detected)
    logger.info('Processing user question', {
      requestId,
      messagePreview: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
      databaseId,
      database,
      operation: 'process_question_start'
    });

    const questionStartTime = Date.now();
    const result = await langchainService.processUserQuestion(message, undefined, database);
    const questionDuration = Date.now() - questionStartTime;

    logger.performance('Question processing completed', {
      requestId,
      databaseId,
      duration: questionDuration,
      operation: 'process_question_end',
      hasResults: !!result.results,
      resultCount: result.results?.length || 0
    });

    const response: ChatResponse = {
      response: result.response,
      query: result.query,
      results: result.results,
      databaseType: actualDatabaseType,
      detectionInfo: result.detectedCollection ? `Collection auto-detected: ${result.detectedCollection}` : undefined,
      usedDatabase: database,
      usedTable: result.detectedCollection || 'auto-detected',
      usedDatabaseId: databaseId
    };

    const totalDuration = Date.now() - startTime;

    logger.info('Chat request completed successfully', {
      requestId,
      databaseId,
      totalDuration,
      questionDuration,
      detectedCollection: result.detectedCollection,
      operation: 'chat_success'
    });

    res.json(response);

  } catch (error) {
    const totalDuration = Date.now() - startTime;

    logger.error('Chat request failed', {
      requestId,
      databaseId: req.body.databaseId,
      totalDuration,
      operation: 'chat_error'
    }, error instanceof Error ? error : new Error(String(error)));

    const errorResponse: ChatResponse = {
      response: 'I apologize, but I encountered an error while processing your request. Please try again.',
      error: error instanceof Error ? error.message : 'Unknown error',
      requestId
    };

    res.status(500).json(errorResponse);
  }
});

// Health check endpoint
router.get('/health', async (req: Request, res: Response) => {
  const startTime = Date.now();
  const requestId = req.requestId;

  try {
    logger.debug('Health check started', { requestId, operation: 'health_check_start' });

    // Test Ollama connection
    const enabledDatabases = databaseManager.getEnabledDatabases();
    let ollamaStatus = false;

    if (enabledDatabases.length > 0) {
      try {
        const firstDbService = databaseManager.getDatabaseService(enabledDatabases[0].id);
        const langchainService = new DatabaseLangChainService(
          firstDbService,
          process.env.OLLAMA_MODEL || 'llama2'
        );
        ollamaStatus = await langchainService.testConnection();

        logger.healthCheck('ollama', ollamaStatus ? 'healthy' : 'unhealthy', {
          requestId,
          model: process.env.OLLAMA_MODEL || 'llama2'
        });
      } catch (error) {
        logger.error('Ollama health check failed', { requestId }, error instanceof Error ? error : new Error(String(error)));
      }
    }

    // Test all database connections
    const databaseStatuses = await databaseManager.getAllDatabasesStatus();

    // Log database health status
    Object.entries(databaseStatuses).forEach(([dbId, status]) => {
      logger.healthCheck(`database_${dbId}`, status ? 'healthy' : 'unhealthy', {
        requestId,
        databaseId: dbId
      });
    });

    const duration = Date.now() - startTime;
    const overallHealthy = ollamaStatus && Object.values(databaseStatuses).every(status => status);

    logger.info('Health check completed', {
      requestId,
      duration,
      overallStatus: overallHealthy ? 'healthy' : 'unhealthy',
      ollamaStatus: ollamaStatus ? 'connected' : 'disconnected',
      databaseCount: Object.keys(databaseStatuses).length,
      operation: 'health_check_end'
    });

    res.json({
      status: 'ok',
      services: {
        ollama: ollamaStatus ? 'connected' : 'disconnected',
        databases: databaseStatuses
      },
      timestamp: new Date().toISOString(),
      duration
    });

  } catch (error) {
    const duration = Date.now() - startTime;

    logger.error('Health check failed', {
      requestId,
      duration,
      operation: 'health_check_error'
    }, error instanceof Error ? error : new Error(String(error)));

    res.status(500).json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      requestId,
      duration
    });
  }
});

// Get available databases
router.get('/databases', async (req: Request, res: Response) => {
  try {
    const enabledDatabases = databaseManager.getEnabledDatabases();
    const databasesInfo: DatabaseInfo[] = [];

    for (const config of enabledDatabases) {
      try {
        const service = databaseManager.getDatabaseService(config.id);
        const isConnected = await service.testConnection();

        if (isConnected) {
          await service.connect();
          const databases = await service.listDatabases();

          databasesInfo.push({
            id: config.id,
            name: config.name,
            type: config.type,
            enabled: config.enabled,
            databases
          });
        } else {
          databasesInfo.push({
            id: config.id,
            name: config.name,
            type: config.type,
            enabled: config.enabled,
            databases: []
          });
        }
      } catch (error) {
        console.error(`Error getting info for database ${config.id}:`, error);
        databasesInfo.push({
          id: config.id,
          name: config.name,
          type: config.type,
          enabled: config.enabled,
          databases: []
        });
      }
    }

    res.json({
      databases: databasesInfo
    });
  } catch (error) {
    console.error('Databases endpoint error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch databases'
    });
  }
});

// Get available tables/collections for a specific database
router.get('/tables', async (req: Request, res: Response) => {
  try {
    const databaseId = req.query.databaseId as string;
    const database = req.query.database as string;

    if (!databaseId) {
      return res.status(400).json({
        error: 'databaseId parameter is required'
      });
    }

    const service = databaseManager.getDatabaseService(databaseId);
    await service.connect(database);
    const tables = await service.listTables(database);
    const databaseType = service.getType();

    res.json({
      databaseId,
      database: database || 'default',
      databaseType,
      tables,
      // For backward compatibility
      collections: databaseType === 'mongodb' ? tables : []
    });
  } catch (error) {
    console.error('Tables endpoint error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch tables'
    });
  }
});

// Get available collections (backward compatibility)
router.get('/collections', async (req: Request, res: Response) => {
  try {
    const database = req.query.database as string || 'test';
    const databaseId = req.query.databaseId as string || 'mongodb';

    const service = databaseManager.getDatabaseService(databaseId);
    await service.connect(database);
    const collections = await service.listTables(database);

    res.json({
      database,
      collections
    });
  } catch (error) {
    console.error('Collections endpoint error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch collections'
    });
  }
});

export default router;
