export type DatabaseType = 'mongodb' | 'postgresql' | 'mysql';

export interface DatabaseConnection {
  uri?: string; // For MongoDB
  host?: string; // For SQL databases
  port?: number;
  database?: string;
  username?: string;
  password?: string;
  options?: Record<string, any>;
}

export interface DatabaseConfig {
  id: string;
  name: string;
  type: DatabaseType;
  enabled: boolean;
  connection: DatabaseConnection;
  defaultDatabase?: string;
  defaultCollection?: string; // For MongoDB
  defaultSchema?: string; // For SQL databases
}

export interface DatabaseConfigs {
  databases: Record<string, DatabaseConfig>;
}

export interface DatabaseSchema {
  database: string;
  collection?: string; // For MongoDB
  table?: string; // For SQL databases
  schema?: string; // For SQL databases
  sampleDocument?: any;
  fields?: string[];
  indexes?: any[];
  exists: boolean;
  documentCount?: number; // For MongoDB
  rowCount?: number; // For SQL databases
}

export interface QueryResult {
  query: string | object;
  results: any[];
  count: number;
  executionTime?: number;
}

export abstract class DatabaseService {
  protected config: DatabaseConfig;
  protected isConnected: boolean = false;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  abstract connect(database?: string): Promise<void>;
  abstract disconnect(): Promise<void>;
  abstract testConnection(): Promise<boolean>;
  abstract listDatabases(): Promise<string[]>;
  abstract listTables(database?: string): Promise<string[]>;
  abstract getTableSchema(tableName: string, database?: string): Promise<DatabaseSchema>;
  abstract executeQuery(query: string | object, options?: any): Promise<QueryResult>;
  abstract getConnectionStatus(): boolean;

  getConfig(): DatabaseConfig {
    return this.config;
  }

  getType(): DatabaseType {
    return this.config.type;
  }
}

export interface DatabasePrompts {
  queryGeneration: string;
  responseGeneration: string;
}

export interface PromptsConfig {
  [key: string]: DatabasePrompts;
}
