import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import chatRoutes from './routes/chat';
import { logger } from './services/Logger';
import { requestLogger, errorLogger, healthCheckLogger } from './middleware/requestLogger';

// Load environment variables from backend directory
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize logging
logger.info('Starting backend server', {
  port: PORT,
  environment: process.env.NODE_ENV || 'development',
  logLevel: logger.getLogLevel()
});

// Request logging middleware (before other middleware)
app.use(healthCheckLogger());
app.use(requestLogger({
  includeBody: process.env.NODE_ENV === 'development',
  includeHeaders: process.env.NODE_ENV === 'development',
  maxBodyLength: 2000
}));

// CORS middleware
app.use(cors({
  origin: true, // Allow all origins since frontend is served from same port
  credentials: true
}));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Increase timeout for AI processing
app.use((req, res, next) => {
  req.setTimeout(120000); // 2 minutes
  res.setTimeout(120000); // 2 minutes
  next();
});

// API Routes
app.use('/api', chatRoutes);

// Serve static files from frontend build
const frontendBuildPath = path.resolve(__dirname, '../../frontend/build');
app.use(express.static(frontendBuildPath));

// Error logging middleware
app.use(errorLogger());

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled application error', {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl || req.url,
    operation: 'unhandled_error'
  }, err);

  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong',
    requestId: req.requestId
  });
});

// Catch all handler: send back React's index.html file for any non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.resolve(frontendBuildPath, 'index.html'));
});

app.listen(PORT, () => {
  logger.info('Backend server started successfully', {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    endpoints: {
      api: `http://localhost:${PORT}/api`,
      health: `http://localhost:${PORT}/api/health`,
      frontend: `http://localhost:${PORT}`
    },
    operation: 'server_start'
  });

  console.log(`🚀 Backend server running on http://localhost:${PORT}`);
  console.log(`📊 API endpoints available at http://localhost:${PORT}/api`);
  console.log(`🔍 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📝 Logs directory: ${path.resolve(__dirname, '../logs')}`);
});
