import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface LogContext {
  requestId?: string;
  userId?: string;
  databaseId?: string;
  operation?: string;
  duration?: number;
  [key: string]: any;
}

export interface LogMetadata extends LogContext {
  timestamp: string;
  service: string;
  environment: string;
}

export class Logger {
  private static instance: Logger;
  private logger!: winston.Logger;
  private logDir: string;

  private constructor() {
    this.logDir = path.resolve(__dirname, '../../logs');
    this.setupLogger();
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private setupLogger(): void {
    const environment = process.env.NODE_ENV || 'development';
    const logLevel = process.env.LOG_LEVEL || (environment === 'production' ? 'info' : 'debug');

    // Custom format for structured logging
    const customFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf((info) => {
        const { timestamp, level, message, service, requestId, ...meta } = info;

        const logEntry = {
          timestamp,
          level: level.toUpperCase(),
          service: service || 'backend',
          message,
          ...(requestId && { requestId }),
          ...meta
        };

        return JSON.stringify(logEntry);
      })
    );

    // Console format for development
    const consoleFormat = winston.format.combine(
      winston.format.timestamp({ format: 'HH:mm:ss.SSS' }),
      winston.format.colorize(),
      winston.format.printf((info) => {
        const { timestamp, level, message, service, requestId, duration, ...meta } = info;

        let logLine = `${timestamp} [${level}] ${service || 'backend'}`;

        if (requestId) {
          logLine += ` [${requestId.substring(0, 8)}]`;
        }

        logLine += `: ${message}`;

        if (duration !== undefined) {
          logLine += ` (${duration}ms)`;
        }

        // Add metadata if present
        const metaKeys = Object.keys(meta);
        if (metaKeys.length > 0) {
          const metaStr = metaKeys
            .map(key => `${key}=${JSON.stringify(meta[key])}`)
            .join(' ');
          logLine += ` | ${metaStr}`;
        }

        return logLine;
      })
    );

    // Create transports
    const transports: winston.transport[] = [];

    // Console transport (always enabled in development)
    if (environment === 'development' || process.env.ENABLE_CONSOLE_LOGS === 'true') {
      transports.push(
        new winston.transports.Console({
          level: logLevel,
          format: consoleFormat,
        })
      );
    }

    // File transports for production
    if (environment === 'production' || process.env.ENABLE_FILE_LOGS === 'true') {
      // Error logs
      transports.push(
        new DailyRotateFile({
          filename: path.join(this.logDir, 'error-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          format: customFormat,
          maxSize: '20m',
          maxFiles: '14d',
          zippedArchive: true,
        })
      );

      // Combined logs
      transports.push(
        new DailyRotateFile({
          filename: path.join(this.logDir, 'combined-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          level: logLevel,
          format: customFormat,
          maxSize: '20m',
          maxFiles: '7d',
          zippedArchive: true,
        })
      );

      // Performance logs
      transports.push(
        new DailyRotateFile({
          filename: path.join(this.logDir, 'performance-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          level: 'info',
          format: customFormat,
          maxSize: '20m',
          maxFiles: '3d',
          zippedArchive: true,
          // Only log performance-related entries
          filter: (info) => info.operation || info.duration !== undefined,
        })
      );
    }

    this.logger = winston.createLogger({
      level: logLevel,
      transports,
      exitOnError: false,
    });

    // Handle uncaught exceptions and unhandled rejections
    this.logger.exceptions.handle(
      new winston.transports.File({
        filename: path.join(this.logDir, 'exceptions.log'),
        format: customFormat,
      })
    );

    this.logger.rejections.handle(
      new winston.transports.File({
        filename: path.join(this.logDir, 'rejections.log'),
        format: customFormat,
      })
    );
  }

  // Core logging methods
  error(message: string, context?: LogContext, error?: Error): void {
    this.log('error', message, context, error);
  }

  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  // Specialized logging methods
  database(message: string, context: LogContext & { databaseId: string }): void {
    this.log('info', message, { ...context, service: 'database' });
  }

  performance(message: string, context: LogContext & { operation: string; duration: number }): void {
    this.log('info', message, { ...context, service: 'performance' });
  }

  security(message: string, context?: LogContext): void {
    this.log('warn', message, { ...context, service: 'security' });
  }

  api(message: string, context: LogContext & { method?: string; url?: string; statusCode?: number }): void {
    this.log('info', message, { ...context, service: 'api' });
  }

  // Request lifecycle logging
  startRequest(method: string, url: string, requestId?: string): string {
    const id = requestId || uuidv4();
    this.api('Request started', {
      requestId: id,
      method,
      url,
      operation: 'request_start'
    });
    return id;
  }

  endRequest(requestId: string, statusCode: number, duration: number, method?: string, url?: string): void {
    this.api('Request completed', {
      requestId,
      method,
      url,
      statusCode,
      duration,
      operation: 'request_end'
    });
  }

  // Database operation logging
  startDatabaseOperation(operation: string, databaseId: string, requestId?: string): string {
    const operationId = uuidv4();
    this.database('Database operation started', {
      requestId,
      operationId,
      databaseId,
      operation: `db_${operation}_start`
    });
    return operationId;
  }

  endDatabaseOperation(
    operationId: string,
    operation: string,
    databaseId: string,
    duration: number,
    success: boolean,
    requestId?: string,
    details?: any
  ): void {
    const level = success ? 'info' : 'error';
    this.log(level, `Database operation ${success ? 'completed' : 'failed'}`, {
      requestId,
      operationId,
      databaseId,
      operation: `db_${operation}_end`,
      duration,
      success,
      ...details
    });
  }

  // AI/LLM operation logging
  startAIOperation(operation: string, model: string, requestId?: string): string {
    const operationId = uuidv4();
    this.log('info', 'AI operation started', {
      requestId,
      operationId,
      model,
      operation: `ai_${operation}_start`,
      service: 'ai'
    });
    return operationId;
  }

  endAIOperation(
    operationId: string,
    operation: string,
    model: string,
    duration: number,
    success: boolean,
    requestId?: string,
    details?: any
  ): void {
    const level = success ? 'info' : 'error';
    this.log(level, `AI operation ${success ? 'completed' : 'failed'}`, {
      requestId,
      operationId,
      model,
      operation: `ai_${operation}_end`,
      duration,
      success,
      service: 'ai',
      ...details
    });
  }

  // Configuration logging
  configLoaded(configType: string, details?: any): void {
    this.log('info', `Configuration loaded: ${configType}`, {
      service: 'config',
      configType,
      ...details
    });
  }

  configError(configType: string, error: Error): void {
    this.log('error', `Configuration error: ${configType}`, {
      service: 'config',
      configType
    }, error);
  }

  // Health check logging
  healthCheck(service: string, status: 'healthy' | 'unhealthy', details?: any): void {
    const level = status === 'healthy' ? 'info' : 'warn';
    this.log(level, `Health check: ${service} is ${status}`, {
      service: 'health',
      healthService: service,
      status,
      ...details
    });
  }

  // Private core logging method
  private log(level: string, message: string, context?: LogContext, error?: Error): void {
    const metadata: any = {
      service: context?.service || 'backend',
      environment: process.env.NODE_ENV || 'development',
      ...context
    };

    if (error) {
      metadata.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      };
    }

    this.logger.log(level, message, metadata);
  }

  // Utility methods
  createChildLogger(service: string, context?: LogContext): Logger {
    const childLogger = Object.create(this);
    childLogger.defaultContext = { service, ...context };
    return childLogger;
  }

  // Method to get current log level
  getLogLevel(): string {
    return this.logger.level;
  }

  // Method to change log level at runtime
  setLogLevel(level: string): void {
    this.logger.level = level;
    this.logger.transports.forEach(transport => {
      transport.level = level;
    });
  }

  // Flush logs (useful for testing)
  async flush(): Promise<void> {
    return new Promise((resolve) => {
      this.logger.on('finish', resolve);
      this.logger.end();
    });
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

// Export types
export type { LogContext, LogMetadata };
