import mysql from 'mysql2/promise';
import { DatabaseService, DatabaseConfig, DatabaseSchema, QueryResult } from '../../types/database';

export class MySQLService extends DatabaseService {
  private pool: mysql.Pool | null = null;

  constructor(config: DatabaseConfig) {
    super(config);
  }

  async connect(database?: string): Promise<void> {
    try {
      if (!this.isConnected) {
        const connectionConfig = {
          host: this.config.connection.host || 'localhost',
          port: this.config.connection.port || 3306,
          database: database || this.config.connection.database || 'mysql',
          user: this.config.connection.username || 'root',
          password: this.config.connection.password || '',
          ...this.config.connection.options
        };

        this.pool = mysql.createPool(connectionConfig);
        
        // Test the connection
        const connection = await this.pool.getConnection();
        connection.release();
        
        this.isConnected = true;
        console.log('Connected to MySQL');
      }
    } catch (error) {
      console.error('Failed to connect to MySQL:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.pool && this.isConnected) {
      await this.pool.end();
      this.pool = null;
      this.isConnected = false;
      console.log('Disconnected from MySQL');
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      if (!this.pool) {
        await this.connect();
      }
      const connection = await this.pool!.getConnection();
      await connection.execute('SELECT 1');
      connection.release();
      return true;
    } catch (error) {
      console.error('MySQL connection test failed:', error);
      return false;
    }
  }

  async listDatabases(): Promise<string[]> {
    try {
      if (!this.pool) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const query = `
        SHOW DATABASES;
      `;

      const [rows] = await this.pool.execute(query) as [any[], any];
      return rows
        .map(row => row.Database)
        .filter(db => !['information_schema', 'performance_schema', 'mysql', 'sys'].includes(db));
    } catch (error) {
      console.error('Error listing databases:', error);
      throw error;
    }
  }

  async listTables(database?: string): Promise<string[]> {
    try {
      if (!this.pool) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const dbName = database || this.config.connection.database || 'mysql';
      const query = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = ? 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name;
      `;

      const [rows] = await this.pool.execute(query, [dbName]) as [any[], any];
      return rows.map(row => row.table_name || row.TABLE_NAME);
    } catch (error) {
      console.error('Error listing tables:', error);
      throw error;
    }
  }

  async getTableSchema(tableName: string, database?: string): Promise<DatabaseSchema> {
    try {
      if (!this.pool) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const dbName = database || this.config.connection.database || 'mysql';

      // Check if table exists
      const tableExistsQuery = `
        SELECT COUNT(*) as count
        FROM information_schema.tables 
        WHERE table_schema = ? AND table_name = ?;
      `;

      const [existsRows] = await this.pool.execute(tableExistsQuery, [dbName, tableName]) as [any[], any];
      const tableExists = existsRows[0].count > 0;

      if (!tableExists) {
        return {
          database: dbName,
          table: tableName,
          schema: dbName,
          sampleDocument: null,
          rowCount: 0,
          fields: [],
          exists: false
        };
      }

      // Get column information
      const columnsQuery = `
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          column_key
        FROM information_schema.columns 
        WHERE table_schema = ? AND table_name = ?
        ORDER BY ordinal_position;
      `;

      // Get row count
      const countQuery = `SELECT COUNT(*) as count FROM \`${dbName}\`.\`${tableName}\`;`;

      // Get sample row
      const sampleQuery = `SELECT * FROM \`${dbName}\`.\`${tableName}\` LIMIT 1;`;

      const [columnsRows] = await this.pool.execute(columnsQuery, [dbName, tableName]) as [any[], any];
      const [countRows] = await this.pool.execute(countQuery) as [any[], any];
      const [sampleRows] = await this.pool.execute(sampleQuery) as [any[], any];

      const fields = columnsRows.map(row => {
        const nullable = row.is_nullable === 'YES' ? '' : ', NOT NULL';
        const key = row.column_key === 'PRI' ? ', PRIMARY KEY' : 
                   row.column_key === 'UNI' ? ', UNIQUE' : '';
        return `${row.column_name} (${row.data_type}${nullable}${key})`;
      });

      return {
        database: dbName,
        table: tableName,
        schema: dbName,
        sampleDocument: sampleRows[0] || null,
        rowCount: countRows[0].count,
        fields,
        exists: true
      };
    } catch (error) {
      console.error('Error getting table schema:', error);
      throw error;
    }
  }

  async executeQuery(query: string | object, options: any = {}): Promise<QueryResult> {
    try {
      if (!this.pool) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const startTime = Date.now();
      let sqlQuery: string;

      if (typeof query === 'object') {
        throw new Error('MySQL service expects SQL string queries, not objects.');
      }

      sqlQuery = query as string;

      // Execute the query
      const [rows] = await this.pool.execute(sqlQuery) as [any[], any];
      const executionTime = Date.now() - startTime;

      return {
        query: sqlQuery,
        results: Array.isArray(rows) ? rows : [rows],
        count: Array.isArray(rows) ? rows.length : 1,
        executionTime
      };
    } catch (error) {
      console.error('Error executing MySQL query:', error);
      throw error;
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected && this.pool !== null;
  }
}
