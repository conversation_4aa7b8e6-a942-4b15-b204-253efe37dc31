import { Pool, PoolClient } from 'pg';
import { DatabaseService, DatabaseConfig, DatabaseSchema, QueryResult } from '../../types/database';

export class PostgreSQLService extends DatabaseService {
  private pool: Pool | null = null;

  constructor(config: DatabaseConfig) {
    super(config);
  }

  async connect(database?: string): Promise<void> {
    try {
      if (!this.isConnected) {
        const connectionConfig = {
          host: this.config.connection.host || 'localhost',
          port: this.config.connection.port || 5432,
          database: database || this.config.connection.database || 'postgres',
          user: this.config.connection.username || 'postgres',
          password: this.config.connection.password || '',
          ...this.config.connection.options
        };

        this.pool = new Pool(connectionConfig);
        
        // Test the connection
        const client = await this.pool.connect();
        client.release();
        
        this.isConnected = true;
        console.log('Connected to PostgreSQL');
      }
    } catch (error) {
      console.error('Failed to connect to PostgreSQL:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.pool && this.isConnected) {
      await this.pool.end();
      this.pool = null;
      this.isConnected = false;
      console.log('Disconnected from PostgreSQL');
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      if (!this.pool) {
        await this.connect();
      }
      const client = await this.pool!.connect();
      await client.query('SELECT 1');
      client.release();
      return true;
    } catch (error) {
      console.error('PostgreSQL connection test failed:', error);
      return false;
    }
  }

  async listDatabases(): Promise<string[]> {
    try {
      if (!this.pool) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const query = `
        SELECT datname 
        FROM pg_database 
        WHERE datistemplate = false 
        AND datname NOT IN ('postgres', 'template0', 'template1')
        ORDER BY datname;
      `;

      const result = await this.pool.query(query);
      return result.rows.map(row => row.datname);
    } catch (error) {
      console.error('Error listing databases:', error);
      throw error;
    }
  }

  async listTables(database?: string): Promise<string[]> {
    try {
      if (!this.pool) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const schema = this.config.defaultSchema || 'public';
      const query = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = $1 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name;
      `;

      const result = await this.pool.query(query, [schema]);
      return result.rows.map(row => row.table_name);
    } catch (error) {
      console.error('Error listing tables:', error);
      throw error;
    }
  }

  async getTableSchema(tableName: string, database?: string): Promise<DatabaseSchema> {
    try {
      if (!this.pool) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const schema = this.config.defaultSchema || 'public';

      // Check if table exists
      const tableExistsQuery = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = $1 AND table_name = $2
        );
      `;

      const existsResult = await this.pool.query(tableExistsQuery, [schema, tableName]);
      const tableExists = existsResult.rows[0].exists;

      if (!tableExists) {
        return {
          database: database || this.config.connection.database || 'postgres',
          table: tableName,
          schema: schema,
          sampleDocument: null,
          rowCount: 0,
          fields: [],
          exists: false
        };
      }

      // Get column information
      const columnsQuery = `
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default
        FROM information_schema.columns 
        WHERE table_schema = $1 AND table_name = $2
        ORDER BY ordinal_position;
      `;

      // Get row count
      const countQuery = `SELECT COUNT(*) as count FROM "${schema}"."${tableName}";`;

      // Get sample row
      const sampleQuery = `SELECT * FROM "${schema}"."${tableName}" LIMIT 1;`;

      const [columnsResult, countResult, sampleResult] = await Promise.all([
        this.pool.query(columnsQuery, [schema, tableName]),
        this.pool.query(countQuery),
        this.pool.query(sampleQuery)
      ]);

      const fields = columnsResult.rows.map(row => 
        `${row.column_name} (${row.data_type}${row.is_nullable === 'NO' ? ', NOT NULL' : ''})`
      );

      return {
        database: database || this.config.connection.database || 'postgres',
        table: tableName,
        schema: schema,
        sampleDocument: sampleResult.rows[0] || null,
        rowCount: parseInt(countResult.rows[0].count),
        fields,
        exists: true
      };
    } catch (error) {
      console.error('Error getting table schema:', error);
      throw error;
    }
  }

  async executeQuery(query: string | object, options: any = {}): Promise<QueryResult> {
    try {
      if (!this.pool) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const startTime = Date.now();
      let sqlQuery: string;

      if (typeof query === 'object') {
        throw new Error('PostgreSQL service expects SQL string queries, not objects.');
      }

      sqlQuery = query as string;

      // Execute the query
      const result = await this.pool.query(sqlQuery);
      const executionTime = Date.now() - startTime;

      return {
        query: sqlQuery,
        results: result.rows,
        count: result.rows.length,
        executionTime
      };
    } catch (error) {
      console.error('Error executing PostgreSQL query:', error);
      throw error;
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected && this.pool !== null;
  }
}
