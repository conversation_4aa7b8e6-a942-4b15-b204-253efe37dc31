import { MongoClient, Db, Collection } from 'mongodb';
import { DatabaseService, DatabaseConfig, DatabaseSchema, QueryResult } from '../../types/database';

export class MongoDBService extends DatabaseService {
  private client: MongoClient;
  private db: Db | null = null;

  constructor(config: DatabaseConfig) {
    super(config);
    this.client = new MongoClient(
      config.connection.uri || 'mongodb://localhost:27017',
      config.connection.options || {}
    );
  }

  async connect(database?: string): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.client.connect();
        this.isConnected = true;
        console.log('Connected to MongoDB');
      }
      const dbName = database || this.config.defaultDatabase || 'test';
      this.db = this.client.db(dbName);
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await this.client.close();
      this.isConnected = false;
      this.db = null;
      console.log('Disconnected from MongoDB');
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.client.db('admin').admin().ping();
      return true;
    } catch (error) {
      console.error('MongoDB connection test failed:', error);
      return false;
    }
  }

  async listDatabases(): Promise<string[]> {
    try {
      const adminDb = this.client.db('admin');
      const result = await adminDb.admin().listDatabases();
      return result.databases.map(db => db.name);
    } catch (error) {
      console.error('Error listing databases:', error);
      throw error;
    }
  }

  async listTables(database?: string): Promise<string[]> {
    try {
      const dbName = database || this.config.defaultDatabase || 'test';
      const db = this.client.db(dbName);
      const collections = await db.listCollections().toArray();
      return collections.map(collection => collection.name);
    } catch (error) {
      console.error('Error listing collections:', error);
      throw error;
    }
  }

  async getTableSchema(collectionName: string, database?: string): Promise<DatabaseSchema> {
    try {
      const dbName = database || this.config.defaultDatabase || 'test';
      const db = this.client.db(dbName);
      const collection = db.collection(collectionName);

      // Check if collection exists
      const collections = await this.listTables(database);
      const collectionExists = collections.includes(collectionName);

      if (!collectionExists) {
        return {
          database: dbName,
          collection: collectionName,
          sampleDocument: null,
          documentCount: 0,
          indexes: [],
          fields: [],
          exists: false
        };
      }

      // Get sample document and count
      const [sampleDoc, count, indexes] = await Promise.all([
        collection.findOne(),
        collection.countDocuments(),
        collection.indexes()
      ]);

      // Extract field names from sample document
      const fields = sampleDoc ? this.extractFieldNames(sampleDoc) : [];

      return {
        database: dbName,
        collection: collectionName,
        sampleDocument: sampleDoc,
        documentCount: count,
        indexes,
        fields,
        exists: true
      };
    } catch (error) {
      console.error('Error getting collection schema:', error);
      throw error;
    }
  }

  async executeQuery(query: string | object, options: any = {}): Promise<QueryResult> {
    try {
      if (!this.db) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const startTime = Date.now();
      let results: any[] = [];
      let actualQuery = query;

      if (typeof query === 'string') {
        // If query is a string, try to parse it as JSON
        try {
          actualQuery = JSON.parse(query);
        } catch (e) {
          throw new Error('Invalid query format. Expected JSON object or valid JSON string.');
        }
      }

      const queryObj = actualQuery as any;
      const collectionName = options.collection || this.config.defaultCollection || 'users';
      const collection = this.db.collection(collectionName);

      // Handle different types of queries
      if (queryObj.aggregate) {
        results = await collection.aggregate(queryObj.aggregate, options).toArray();
      } else if (queryObj.find !== undefined) {
        const cursor = collection.find(queryObj.find, options);
        if (queryObj.limit) cursor.limit(queryObj.limit);
        if (queryObj.sort) cursor.sort(queryObj.sort);
        results = await cursor.toArray();
      } else {
        // Default to find with the query as filter
        results = await collection.find(queryObj, options).limit(10).toArray();
      }

      const executionTime = Date.now() - startTime;

      return {
        query: actualQuery,
        results,
        count: results.length,
        executionTime
      };
    } catch (error) {
      console.error('Error executing MongoDB query:', error);
      throw error;
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  private extractFieldNames(obj: any, prefix: string = ''): string[] {
    const fields: string[] = [];
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        fields.push(fullKey);
        
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          // Recursively get nested fields (limit depth to avoid infinite recursion)
          if (prefix.split('.').length < 3) {
            fields.push(...this.extractFieldNames(obj[key], fullKey));
          }
        }
      }
    }
    
    return fields;
  }

  // Legacy methods for backward compatibility
  getDatabase(): Db {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db;
  }

  getCollection(collectionName: string): Collection {
    return this.getDatabase().collection(collectionName);
  }

  async listCollections(): Promise<string[]> {
    return this.listTables();
  }

  async getCollectionSchema(collectionName: string): Promise<any> {
    const schema = await this.getTableSchema(collectionName);
    return {
      collection: schema.collection,
      sampleDocument: schema.sampleDocument,
      documentCount: schema.documentCount,
      indexes: schema.indexes,
      fields: schema.fields,
      exists: schema.exists
    };
  }
}
