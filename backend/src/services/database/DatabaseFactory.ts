import { DatabaseService, DatabaseConfig, DatabaseType } from '../../types/database';
import { MongoDBService } from './MongoDBService';
import { PostgreSQLService } from './PostgreSQLService';
import { MySQLService } from './MySQLService';

export class DatabaseFactory {
  static createDatabaseService(config: DatabaseConfig): DatabaseService {
    switch (config.type) {
      case 'mongodb':
        return new MongoDBService(config);
      case 'postgresql':
        return new PostgreSQLService(config);
      case 'mysql':
        return new MySQLService(config);
      default:
        throw new Error(`Unsupported database type: ${config.type}`);
    }
  }

  static getSupportedDatabaseTypes(): DatabaseType[] {
    return ['mongodb', 'postgresql', 'mysql'];
  }
}
