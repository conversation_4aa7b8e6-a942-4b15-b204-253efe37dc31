import fs from 'fs';
import path from 'path';
import { DatabaseConfigs, DatabaseConfig, PromptsConfig } from '../types/database';
import { logger } from './Logger';

export class ConfigManager {
  private static instance: ConfigManager;
  private databaseConfigs: DatabaseConfigs | null = null;
  private promptsConfig: PromptsConfig | null = null;
  private configPath: string;
  private promptsPath: string;

  private constructor() {
    this.configPath = path.resolve(__dirname, '../config/database-config.json');
    this.promptsPath = path.resolve(__dirname, '../config/prompts.json');

    logger.info('ConfigManager initialized', {
      configPath: this.configPath,
      promptsPath: this.promptsPath,
      service: 'config_manager',
      operation: 'init'
    });
  }

  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  getDatabaseConfigs(): DatabaseConfigs {
    if (!this.databaseConfigs) {
      this.loadDatabaseConfigs();
    }
    return this.databaseConfigs!;
  }

  getPromptsConfig(): PromptsConfig {
    if (!this.promptsConfig) {
      this.loadPromptsConfig();
    }
    return this.promptsConfig!;
  }

  getEnabledDatabases(): DatabaseConfig[] {
    const configs = this.getDatabaseConfigs();
    return Object.values(configs.databases).filter(config => config.enabled);
  }

  getDatabaseConfig(databaseId: string): DatabaseConfig | null {
    const configs = this.getDatabaseConfigs();
    return configs.databases[databaseId] || null;
  }

  getPromptForDatabase(databaseType: string, promptType: 'queryGeneration' | 'responseGeneration'): string {
    const prompts = this.getPromptsConfig();
    return prompts[databaseType]?.[promptType] || '';
  }

  private loadDatabaseConfigs(): void {
    try {
      logger.debug('Loading database configuration', {
        configPath: this.configPath,
        operation: 'load_database_config_start'
      });

      const configData = fs.readFileSync(this.configPath, 'utf8');
      this.databaseConfigs = JSON.parse(configData);

      const enabledCount = Object.values(this.databaseConfigs!.databases).filter(db => db.enabled).length;

      logger.configLoaded('database', {
        totalDatabases: Object.keys(this.databaseConfigs!.databases).length,
        enabledDatabases: enabledCount,
        configPath: this.configPath
      });

    } catch (error) {
      logger.configError('database', error instanceof Error ? error : new Error(String(error)));
      throw new Error('Failed to load database configuration');
    }
  }

  private loadPromptsConfig(): void {
    try {
      logger.debug('Loading prompts configuration', {
        promptsPath: this.promptsPath,
        operation: 'load_prompts_config_start'
      });

      const promptsData = fs.readFileSync(this.promptsPath, 'utf8');
      this.promptsConfig = JSON.parse(promptsData);

      const databaseTypes = Object.keys(this.promptsConfig!);

      logger.configLoaded('prompts', {
        databaseTypes,
        typeCount: databaseTypes.length,
        promptsPath: this.promptsPath
      });

    } catch (error) {
      logger.configError('prompts', error instanceof Error ? error : new Error(String(error)));
      throw new Error('Failed to load prompts configuration');
    }
  }

  // Method to reload configurations (useful for development)
  reloadConfigs(): void {
    this.databaseConfigs = null;
    this.promptsConfig = null;
    this.loadDatabaseConfigs();
    this.loadPromptsConfig();
  }

  // Method to update database configuration
  updateDatabaseConfig(databaseId: string, updates: Partial<DatabaseConfig>): void {
    const configs = this.getDatabaseConfigs();
    if (configs.databases[databaseId]) {
      configs.databases[databaseId] = { ...configs.databases[databaseId], ...updates };
      this.saveDatabaseConfigs(configs);
    } else {
      throw new Error(`Database configuration '${databaseId}' not found`);
    }
  }

  // Method to add new database configuration
  addDatabaseConfig(config: DatabaseConfig): void {
    const configs = this.getDatabaseConfigs();
    configs.databases[config.id] = config;
    this.saveDatabaseConfigs(configs);
  }

  // Method to remove database configuration
  removeDatabaseConfig(databaseId: string): void {
    const configs = this.getDatabaseConfigs();
    if (configs.databases[databaseId]) {
      delete configs.databases[databaseId];
      this.saveDatabaseConfigs(configs);
    } else {
      throw new Error(`Database configuration '${databaseId}' not found`);
    }
  }

  private saveDatabaseConfigs(configs: DatabaseConfigs): void {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(configs, null, 2));
      this.databaseConfigs = configs;
    } catch (error) {
      console.error('Error saving database configuration:', error);
      throw new Error('Failed to save database configuration');
    }
  }
}
