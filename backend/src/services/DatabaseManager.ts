import { DatabaseService, DatabaseConfig } from '../types/database';
import { DatabaseFactory } from './database/DatabaseFactory';
import { ConfigManager } from './ConfigManager';
import { logger } from './Logger';

export class DatabaseManager {
  private static instance: DatabaseManager;
  private databaseServices: Map<string, DatabaseService> = new Map();
  private configManager: ConfigManager;

  private constructor() {
    this.configManager = ConfigManager.getInstance();
    logger.info('DatabaseManager initialized', {
      service: 'database_manager',
      operation: 'init'
    });
  }

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  getDatabaseService(databaseId: string): DatabaseService {
    logger.debug('Getting database service', {
      databaseId,
      cached: this.databaseServices.has(databaseId),
      operation: 'get_database_service'
    });

    // Check if service is already cached
    if (this.databaseServices.has(databaseId)) {
      logger.debug('Returning cached database service', { databaseId });
      return this.databaseServices.get(databaseId)!;
    }

    // Get configuration for the database
    const config = this.configManager.getDatabaseConfig(databaseId);
    if (!config) {
      logger.error('Database configuration not found', { databaseId });
      throw new Error(`Database configuration not found for ID: ${databaseId}`);
    }

    if (!config.enabled) {
      logger.warn('Database not enabled', { databaseId });
      throw new Error(`Database '${databaseId}' is not enabled`);
    }

    // Create new service instance
    logger.info('Creating new database service', {
      databaseId,
      databaseType: config.type,
      operation: 'create_database_service'
    });

    const service = DatabaseFactory.createDatabaseService(config);
    this.databaseServices.set(databaseId, service);

    logger.info('Database service created and cached', { databaseId });
    return service;
  }

  getEnabledDatabases(): DatabaseConfig[] {
    return this.configManager.getEnabledDatabases();
  }

  async testDatabaseConnection(databaseId: string): Promise<boolean> {
    const startTime = Date.now();

    try {
      logger.debug('Testing database connection', { databaseId, operation: 'test_connection_start' });

      const service = this.getDatabaseService(databaseId);
      const result = await service.testConnection();
      const duration = Date.now() - startTime;

      logger.info('Database connection test completed', {
        databaseId,
        success: result,
        duration,
        operation: 'test_connection_end'
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('Database connection test failed', {
        databaseId,
        duration,
        operation: 'test_connection_error'
      }, error instanceof Error ? error : new Error(String(error)));

      return false;
    }
  }

  async getAllDatabasesStatus(): Promise<Record<string, boolean>> {
    const enabledDatabases = this.getEnabledDatabases();
    const statusPromises = enabledDatabases.map(async (config) => {
      const status = await this.testDatabaseConnection(config.id);
      return { id: config.id, status };
    });

    const results = await Promise.all(statusPromises);
    const statusMap: Record<string, boolean> = {};

    results.forEach(({ id, status }) => {
      statusMap[id] = status;
    });

    return statusMap;
  }

  async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.databaseServices.values()).map(
      service => service.disconnect().catch(error =>
        console.error('Error disconnecting database service:', error)
      )
    );

    await Promise.all(disconnectPromises);
    this.databaseServices.clear();
  }

  // Method to refresh configurations (useful for development)
  refreshConfigurations(): void {
    this.configManager.reloadConfigs();
    // Clear cached services to force recreation with new configs
    this.disconnectAll();
  }
}
