import { DatabaseService, DatabaseConfig } from '../types/database';
import { DatabaseFactory } from './database/DatabaseFactory';
import { ConfigManager } from './ConfigManager';

export class DatabaseManager {
  private static instance: DatabaseManager;
  private databaseServices: Map<string, DatabaseService> = new Map();
  private configManager: ConfigManager;

  private constructor() {
    this.configManager = ConfigManager.getInstance();
  }

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  getDatabaseService(databaseId: string): DatabaseService {
    // Check if service is already cached
    if (this.databaseServices.has(databaseId)) {
      return this.databaseServices.get(databaseId)!;
    }

    // Get configuration for the database
    const config = this.configManager.getDatabaseConfig(databaseId);
    if (!config) {
      throw new Error(`Database configuration not found for ID: ${databaseId}`);
    }

    if (!config.enabled) {
      throw new Error(`Database '${databaseId}' is not enabled`);
    }

    // Create new service instance
    const service = DatabaseFactory.createDatabaseService(config);
    this.databaseServices.set(databaseId, service);

    return service;
  }

  getEnabledDatabases(): DatabaseConfig[] {
    return this.configManager.getEnabledDatabases();
  }

  async testDatabaseConnection(databaseId: string): Promise<boolean> {
    try {
      const service = this.getDatabaseService(databaseId);
      return await service.testConnection();
    } catch (error) {
      console.error(`Error testing connection for database ${databaseId}:`, error);
      return false;
    }
  }

  async getAllDatabasesStatus(): Promise<Record<string, boolean>> {
    const enabledDatabases = this.getEnabledDatabases();
    const statusPromises = enabledDatabases.map(async (config) => {
      const status = await this.testDatabaseConnection(config.id);
      return { id: config.id, status };
    });

    const results = await Promise.all(statusPromises);
    const statusMap: Record<string, boolean> = {};
    
    results.forEach(({ id, status }) => {
      statusMap[id] = status;
    });

    return statusMap;
  }

  async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.databaseServices.values()).map(
      service => service.disconnect().catch(error => 
        console.error('Error disconnecting database service:', error)
      )
    );

    await Promise.all(disconnectPromises);
    this.databaseServices.clear();
  }

  // Method to refresh configurations (useful for development)
  refreshConfigurations(): void {
    this.configManager.reloadConfigs();
    // Clear cached services to force recreation with new configs
    this.disconnectAll();
  }
}
