import { ChatOllama } from '@langchain/community/chat_models/ollama';
import { PromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { DatabaseService, DatabaseSchema, QueryResult } from '../types/database';
import { ConfigManager } from './ConfigManager';
import { logger } from './Logger';

export class DatabaseLangChainService {
  private llm: ChatOllama;
  private databaseService: DatabaseService;
  private configManager: ConfigManager;

  constructor(databaseService: DatabaseService, modelName: string = 'llama2') {
    this.databaseService = databaseService;
    this.configManager = ConfigManager.getInstance();
    this.llm = new ChatOllama({
      baseUrl: 'http://localhost:11434',
      model: modelName,
      temperature: 0.1,
    });

    logger.info('DatabaseLangChainService initialized', {
      databaseType: databaseService.getType(),
      model: modelName,
      service: 'langchain',
      operation: 'init'
    });
  }

  private createQueryGenerationPrompt(databaseType: string): PromptTemplate {
    const promptTemplate = this.configManager.getPromptForDatabase(databaseType, 'queryGeneration');
    if (!promptTemplate) {
      throw new Error(`No query generation prompt found for database type: ${databaseType}`);
    }
    return PromptTemplate.fromTemplate(promptTemplate);
  }

  private createResponseGenerationPrompt(databaseType: string): PromptTemplate {
    const promptTemplate = this.configManager.getPromptForDatabase(databaseType, 'responseGeneration');
    if (!promptTemplate) {
      throw new Error(`No response generation prompt found for database type: ${databaseType}`);
    }
    return PromptTemplate.fromTemplate(promptTemplate);
  }

  private createIntentClassificationPrompt(): PromptTemplate {
    return PromptTemplate.fromTemplate(`
Analyze the following question and determine if it's asking about database/data or if it's a general conversation.

Question: "{question}"
Available Fields: {fields}

Database-related questions include:
- Counting records ("How many users?", "Count of orders")
- Querying data ("Show me users", "Find active customers")
- Data analysis ("Average age", "Total sales")
- Filtering/searching ("Users from New York", "Orders this month")
- Data statistics ("Most popular product", "Latest entries")

General conversation includes:
- Greetings ("Hello", "Hi there")
- General questions ("How are you?", "What can you do?")
- Non-data topics ("Tell me a joke", "What's the weather?")
- System questions not about data ("What is this app?", "How does this work?")

Respond with ONLY one word:
- "DATABASE" if the question is asking about data/database
- "GENERAL" if the question is general conversation

Classification:
`);
  }

  private createGeneralConversationPrompt(): PromptTemplate {
    return PromptTemplate.fromTemplate(`
You are a helpful database assistant. The user asked a general question that's not related to querying data.

User Question: "{question}"

Respond in a friendly, helpful way. Let them know that you specialize in helping with database queries and data analysis. If appropriate, suggest how they might ask database-related questions.

Keep your response conversational and helpful.

Response:
`);
  }

  private createCollectionDetectionPrompt(): PromptTemplate {
    return PromptTemplate.fromTemplate(
      `You are a database expert. Analyze the user's question and determine which collection/table they are most likely asking about.

User Question: "{question}"

Available Collections/Tables:
{availableCollections}

Analyze the question for:
1. Explicit collection/table names mentioned
2. Data types that suggest specific collections (users, orders, products, etc.)
3. Context clues about what kind of data they want
4. Common naming patterns

Respond with just the collection/table name that best matches the question.
If you cannot determine a specific collection, respond with the most likely default.
If the question mentions multiple collections, choose the primary one.

Collection Name:`
    );
  }

  private async classifyIntent(
    question: string,
    fields: string[]
  ): Promise<'DATABASE' | 'GENERAL'> {
    try {
      const intentPrompt = this.createIntentClassificationPrompt();
      const intentChain = RunnableSequence.from([
        intentPrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      console.log(`Classifying intent for: "${question}"`);
      const intentResult = await Promise.race([
        intentChain.invoke({
          question,
          fields: fields.join(', '),
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Intent classification timed out')), 30000)
        )
      ]) as string;

      const classification = intentResult.trim().toUpperCase();
      console.log(`Intent classified as: ${classification}`);

      // Default to DATABASE if classification is unclear
      return classification.includes('DATABASE') ? 'DATABASE' :
             classification.includes('GENERAL') ? 'GENERAL' : 'DATABASE';
    } catch (error) {
      console.error('Error classifying intent:', error);
      // Default to DATABASE on error to maintain existing functionality
      return 'DATABASE';
    }
  }

  private async handleGeneralConversation(
    question: string
  ): Promise<{ response: string; query?: any; results?: any[] }> {
    try {
      const conversationPrompt = this.createGeneralConversationPrompt();
      const conversationChain = RunnableSequence.from([
        conversationPrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      console.log('Handling general conversation...');
      const response = await Promise.race([
        conversationChain.invoke({ question }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('General conversation timed out')), 30000)
        )
      ]) as string;

      return {
        response: response.trim(),
        query: undefined,
        results: undefined,
      };
    } catch (error) {
      console.error('Error handling general conversation:', error);
      return {
        response: "Hello! I'm a database assistant that helps you query and analyze your data. You can ask me questions like 'How many users do we have?' or 'Show me all active customers'. What would you like to know about your data?",
        query: undefined,
        results: undefined,
      };
    }
  }

  private async detectCollectionFromQuestion(
    question: string,
    databaseName?: string
  ): Promise<string> {
    try {
      console.log(`Detecting collection for question: "${question}"`);

      // Get available collections/tables
      const availableCollections = await this.databaseService.listTables(databaseName);

      if (availableCollections.length === 0) {
        console.log('No collections found, using default');
        return this.databaseService.getType() === 'mongodb' ? 'users' : 'users';
      }

      if (availableCollections.length === 1) {
        console.log(`Only one collection available: ${availableCollections[0]}`);
        return availableCollections[0];
      }

      // Use AI to detect the most appropriate collection
      const detectionPrompt = this.createCollectionDetectionPrompt();
      const detectionChain = RunnableSequence.from([
        detectionPrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      const detectionResponse = await Promise.race([
        detectionChain.invoke({
          question,
          availableCollections: availableCollections.join(', ')
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Collection detection timed out')), 30000)
        )
      ]) as string;

      const detectedCollection = detectionResponse.trim();
      console.log(`AI detected collection: ${detectedCollection}`);

      // Validate that the detected collection exists
      if (availableCollections.includes(detectedCollection)) {
        return detectedCollection;
      }

      // If AI response doesn't match available collections, find the best match
      const lowerDetected = detectedCollection.toLowerCase();
      const bestMatch = availableCollections.find(col =>
        col.toLowerCase().includes(lowerDetected) ||
        lowerDetected.includes(col.toLowerCase())
      );

      if (bestMatch) {
        console.log(`Using best match: ${bestMatch}`);
        return bestMatch;
      }

      // Fall back to first available collection
      console.log(`No match found, using first available: ${availableCollections[0]}`);
      return availableCollections[0];

    } catch (error) {
      console.error('Error detecting collection:', error);
      // Fall back to default
      return this.databaseService.getType() === 'mongodb' ? 'users' : 'users';
    }
  }

  async processUserQuestion(
    question: string,
    tableName?: string,
    databaseName?: string
  ): Promise<{ response: string; query?: any; results?: any[]; detectedCollection?: string }> {
    const startTime = Date.now();
    const operationId = logger.startAIOperation('process_question', this.llm.model);

    try {
      const databaseType = this.databaseService.getType();

      logger.info('Processing user question', {
        operationId,
        questionLength: question.length,
        databaseType,
        tableName,
        databaseName,
        operation: 'process_question_start'
      });

      // Connect to database first
      const connectStartTime = Date.now();
      await this.databaseService.connect(databaseName);
      const connectDuration = Date.now() - connectStartTime;

      logger.database('Database connection established', {
        operationId,
        databaseId: this.databaseService.getConfig().id,
        databaseName,
        duration: connectDuration,
        operation: 'database_connect'
      });

      // If no table name provided, detect it from the question
      let finalTableName = tableName;
      let detectedCollection: string | undefined;

      if (!finalTableName) {
        const detectionStartTime = Date.now();
        finalTableName = await this.detectCollectionFromQuestion(question, databaseName);
        detectedCollection = finalTableName;
        const detectionDuration = Date.now() - detectionStartTime;

        logger.info('Collection auto-detected', {
          operationId,
          detectedCollection: finalTableName,
          duration: detectionDuration,
          operation: 'collection_detection'
        });
      }

      // Get schema for the determined table/collection
      const schemaStartTime = Date.now();
      const schema = await this.databaseService.getTableSchema(finalTableName, databaseName);
      const schemaDuration = Date.now() - schemaStartTime;

      logger.database('Schema retrieved', {
        operationId,
        tableName: finalTableName,
        fieldCount: schema.fields?.length || 0,
        recordCount: schema.documentCount || schema.rowCount || 0,
        exists: schema.exists,
        duration: schemaDuration,
        operation: 'schema_retrieval'
      });

      // First, classify the intent of the question
      const intentStartTime = Date.now();
      const intent = await this.classifyIntent(question, schema.fields || []);
      const intentDuration = Date.now() - intentStartTime;

      logger.info('Intent classified', {
        operationId,
        intent,
        duration: intentDuration,
        operation: 'intent_classification'
      });

      // If it's general conversation, handle it without database operations
      if (intent === 'GENERAL') {
        const conversationResult = await this.handleGeneralConversation(question);
        const totalDuration = Date.now() - startTime;

        logger.endAIOperation(operationId, 'process_question', this.llm.model, totalDuration, true, undefined, {
          intent: 'GENERAL',
          responseLength: conversationResult.response.length
        });

        return conversationResult;
      }

      // Check if table/collection exists and has data
      if (!schema.exists) {
        const entityName = databaseType === 'mongodb' ? 'collection' : 'table';
        const dbName = databaseName || 'default database';
        const response = `The ${entityName} '${finalTableName}' doesn't exist in the '${dbName}' database. You might want to create some data first or check if you're using the correct ${entityName} name.`;

        const totalDuration = Date.now() - startTime;
        logger.warn('Table/collection does not exist', {
          operationId,
          tableName: finalTableName,
          databaseName: dbName,
          operation: 'table_not_found'
        });

        logger.endAIOperation(operationId, 'process_question', this.llm.model, totalDuration, false, undefined, {
          reason: 'table_not_found',
          tableName: finalTableName
        });

        return {
          response,
          query: undefined,
          results: undefined,
          detectedCollection
        };
      }

      const recordCount = schema.documentCount || schema.rowCount || 0;
      if (recordCount === 0) {
        const entityName = databaseType === 'mongodb' ? 'collection' : 'table';
        const response = `The ${entityName} '${finalTableName}' exists but is empty. You might want to add some data first before querying.`;

        const totalDuration = Date.now() - startTime;
        logger.warn('Table/collection is empty', {
          operationId,
          tableName: finalTableName,
          operation: 'table_empty'
        });

        logger.endAIOperation(operationId, 'process_question', this.llm.model, totalDuration, false, undefined, {
          reason: 'table_empty',
          tableName: finalTableName
        });

        return {
          response,
          query: undefined,
          results: undefined,
          detectedCollection
        };
      }

      // Create schema description for the prompt
      const schemaDescription = this.formatSchemaForPrompt(schema, databaseType);

      // Generate query using LangChain
      const queryPrompt = this.createQueryGenerationPrompt(databaseType);
      const queryChain = RunnableSequence.from([
        queryPrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      logger.info('Generating query', {
        operationId,
        questionPreview: question.substring(0, 100),
        databaseType,
        operation: 'query_generation_start'
      });

      const queryGenStartTime = Date.now();
      const queryResponse = await Promise.race([
        queryChain.invoke({
          schema: schemaDescription,
          question: question,
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Query generation timed out')), 60000)
        )
      ]) as string;
      const queryGenDuration = Date.now() - queryGenStartTime;

      logger.info('Query generated', {
        operationId,
        duration: queryGenDuration,
        responseLength: queryResponse.length,
        operation: 'query_generation_end'
      });

      // Parse the generated query
      let parsedQuery: string | object;
      const parseStartTime = Date.now();

      try {
        if (databaseType === 'mongodb') {
          // Clean the response to extract JSON for MongoDB
          const cleanedResponse = this.cleanQueryResponse(queryResponse);
          parsedQuery = JSON.parse(cleanedResponse);
        } else {
          // For SQL databases, extract the SQL query
          parsedQuery = this.extractSQLQuery(queryResponse);
        }

        const parseDuration = Date.now() - parseStartTime;
        logger.info('Query parsed successfully', {
          operationId,
          queryType: typeof parsedQuery,
          duration: parseDuration,
          operation: 'query_parsing_success'
        });

      } catch (parseError) {
        const parseDuration = Date.now() - parseStartTime;
        const totalDuration = Date.now() - startTime;

        logger.error('Failed to parse generated query', {
          operationId,
          duration: parseDuration,
          operation: 'query_parsing_error'
        }, parseError instanceof Error ? parseError : new Error(String(parseError)));

        logger.endAIOperation(operationId, 'process_question', this.llm.model, totalDuration, false, undefined, {
          reason: 'query_parsing_failed',
          rawQuery: queryResponse.substring(0, 200)
        });

        return {
          response: 'I had trouble understanding your question. Could you please rephrase it or be more specific?',
          query: queryResponse,
          results: undefined,
          detectedCollection
        };
      }

      // Execute the query
      logger.info('Executing query', {
        operationId,
        tableName: finalTableName,
        queryPreview: typeof parsedQuery === 'object' ?
          JSON.stringify(parsedQuery).substring(0, 200) :
          parsedQuery.substring(0, 200),
        operation: 'query_execution_start'
      });

      const queryExecStartTime = Date.now();
      const queryResult = await this.databaseService.executeQuery(parsedQuery, { collection: finalTableName, table: finalTableName });
      const queryExecDuration = Date.now() - queryExecStartTime;

      logger.performance('Query executed', {
        operationId,
        tableName: finalTableName,
        duration: queryExecDuration,
        resultCount: queryResult.count,
        operation: 'query_execution_end'
      });

      // Generate natural language response
      const responsePrompt = this.createResponseGenerationPrompt(databaseType);
      const responseChain = RunnableSequence.from([
        responsePrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      logger.info('Generating natural language response', {
        operationId,
        resultCount: queryResult.count,
        operation: 'response_generation_start'
      });

      const responseGenStartTime = Date.now();
      const naturalResponse = await Promise.race([
        responseChain.invoke({
          question: question,
          query: typeof parsedQuery === 'object' ? JSON.stringify(parsedQuery, null, 2) : parsedQuery,
          results: JSON.stringify(queryResult.results.slice(0, 3), null, 2), // Show first 3 results for context
          count: queryResult.count,
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Response generation timed out')), 60000)
        )
      ]) as string;
      const responseGenDuration = Date.now() - responseGenStartTime;
      const totalDuration = Date.now() - startTime;

      logger.info('Natural language response generated', {
        operationId,
        duration: responseGenDuration,
        responseLength: naturalResponse.length,
        operation: 'response_generation_end'
      });

      logger.endAIOperation(operationId, 'process_question', this.llm.model, totalDuration, true, undefined, {
        intent: 'DATABASE',
        tableName: finalTableName,
        resultCount: queryResult.count,
        queryGenDuration,
        queryExecDuration,
        responseGenDuration
      });

      return {
        response: naturalResponse,
        query: parsedQuery,
        results: queryResult.results,
        detectedCollection
      };
    } catch (error) {
      const totalDuration = Date.now() - startTime;

      logger.error('Error processing user question', {
        operationId,
        duration: totalDuration,
        operation: 'process_question_error'
      }, error instanceof Error ? error : new Error(String(error)));

      logger.endAIOperation(operationId, 'process_question', this.llm.model, totalDuration, false, undefined, {
        reason: 'unexpected_error',
        errorType: error instanceof Error ? error.name : 'unknown'
      });

      return {
        response: 'I encountered an error while processing your question. Please try again or rephrase your question.',
        query: undefined,
        results: undefined,
        detectedCollection: undefined
      };
    }
  }

  private formatSchemaForPrompt(schema: DatabaseSchema, databaseType: string): string {
    let description = '';

    if (databaseType === 'mongodb') {
      description = `Collection: ${schema.collection}\n`;
      description += `Document Count: ${schema.documentCount}\n`;
    } else {
      description = `Table: ${schema.table}\n`;
      description += `Schema: ${schema.schema}\n`;
      description += `Row Count: ${schema.rowCount}\n`;
    }

    if (schema.fields && schema.fields.length > 0) {
      const fieldLabel = databaseType === 'mongodb' ? 'Available Fields' : 'Available Columns';
      description += `\n${fieldLabel}:\n`;
      schema.fields.forEach((field: string) => {
        description += `- ${field}\n`;
      });
    }

    if (schema.sampleDocument) {
      const sampleLabel = databaseType === 'mongodb' ? 'Sample Document Structure' : 'Sample Row Structure';
      description += `\n${sampleLabel}:\n`;
      description += JSON.stringify(schema.sampleDocument, null, 2);
    }

    return description;
  }

  private cleanQueryResponse(response: string): string {
    // Remove any markdown formatting
    let cleaned = response.replace(/```json\s*/g, '').replace(/```\s*/g, '');

    // Remove any explanatory text before or after the JSON
    const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleaned = jsonMatch[0];
    }

    // Remove any trailing text after the JSON
    const lines = cleaned.split('\n');
    let jsonLines = [];
    let braceCount = 0;
    let foundStart = false;

    for (const line of lines) {
      for (const char of line) {
        if (char === '{') {
          braceCount++;
          foundStart = true;
        } else if (char === '}') {
          braceCount--;
        }
      }

      if (foundStart) {
        jsonLines.push(line);
      }

      if (foundStart && braceCount === 0) {
        break;
      }
    }

    return jsonLines.join('\n').trim();
  }

  private extractSQLQuery(response: string): string {
    // Remove any markdown formatting
    let cleaned = response.replace(/```sql\s*/g, '').replace(/```\s*/g, '');

    // Look for SQL query patterns
    const sqlMatch = cleaned.match(/(SELECT[\s\S]*?;?)|(INSERT[\s\S]*?;?)|(UPDATE[\s\S]*?;?)|(DELETE[\s\S]*?;?)/i);
    if (sqlMatch) {
      cleaned = sqlMatch[0];
    }

    // Clean up the query
    cleaned = cleaned.trim();
    if (!cleaned.endsWith(';')) {
      cleaned += ';';
    }

    return cleaned;
  }

  async testConnection(): Promise<boolean> {
    const startTime = Date.now();

    try {
      logger.debug('Testing Ollama connection', {
        model: this.llm.model,
        operation: 'ollama_test_start'
      });

      const response = await this.llm.invoke('Hello, are you working?');
      const duration = Date.now() - startTime;

      logger.info('Ollama connection test successful', {
        model: this.llm.model,
        duration,
        operation: 'ollama_test_success'
      });

      return true;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('Ollama connection test failed', {
        model: this.llm.model,
        duration,
        operation: 'ollama_test_error'
      }, error instanceof Error ? error : new Error(String(error)));

      return false;
    }
  }
}
