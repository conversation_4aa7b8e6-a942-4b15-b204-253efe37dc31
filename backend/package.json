{"name": "backend", "version": "1.0.0", "description": "Backend API for multi-database chat system", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && npm run copy-config", "copy-config": "mkdir -p dist/config && cp src/config/*.json dist/config/", "start": "node dist/index.js", "type-check": "tsc --noEmit"}, "dependencies": {"@langchain/community": "^0.0.20", "@langchain/core": "^0.1.0", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-winston": "^4.2.0", "langchain": "^0.1.0", "mongodb": "^6.3.0", "mysql2": "^3.14.1", "pg": "^8.16.2", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "tsx": "^4.6.2", "typescript": "^5.3.3"}}