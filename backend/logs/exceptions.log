{"date":"Sat Jun 21 2025 14:58:30 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:68:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:72:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[16.01318359375,17.50537109375,15.7802734375],"uptime":274004},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18410894,"external":22692497,"heapTotal":59211776,"heapUsed":28890072,"rss":155467776},"pid":97210,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:68:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:72:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":68,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":72,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 14:58:41 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:82:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[14.162109375,17.0224609375,15.6376953125],"uptime":274015},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18374464,"external":22656083,"heapTotal":58949632,"heapUsed":28438608,"rss":162021376},"pid":97332,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:82:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":82,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 14:58:51 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[13.01904296875,16.67724609375,15.53076171875],"uptime":274025},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18375067,"external":22656686,"heapTotal":59473920,"heapUsed":28291192,"rss":158367744},"pid":97456,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 14:59:04 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[11.47802734375,16.22607421875,15.3837890625],"uptime":274038},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18379075,"external":22660694,"heapTotal":59211776,"heapUsed":28581696,"rss":153321472},"pid":97584,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 14:59:14 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[10.388671875,15.8388671875,15.255859375],"uptime":274048},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18379605,"external":22661224,"heapTotal":59211776,"heapUsed":28658744,"rss":147800064},"pid":97701,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 14:59:42 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[9.02783203125,14.97900390625,14.96240234375],"uptime":274076},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18371580,"external":22653199,"heapTotal":59736064,"heapUsed":27961488,"rss":150470656},"pid":98149,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 14:59:59 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[7.98095703125,14.4560546875,14.77587890625],"uptime":274093},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18380862,"external":22662481,"heapTotal":59736064,"heapUsed":28313848,"rss":153649152},"pid":98283,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:00:11 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[7.27099609375,13.9765625,14.5986328125],"uptime":274105},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18381212,"external":22662831,"heapTotal":59473920,"heapUsed":28394152,"rss":152420352},"pid":98406,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:00:21 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[6.93408203125,13.68115234375,14.48583984375],"uptime":274115},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18373346,"external":22654965,"heapTotal":59736064,"heapUsed":27873368,"rss":151126016},"pid":98538,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:00:47 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[7.458984375,13.23095703125,14.29736328125],"uptime":274141},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18383444,"external":22665063,"heapTotal":58949632,"heapUsed":28063584,"rss":148455424},"pid":98883,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:00:56 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[7.0068359375,12.943359375,14.1826171875],"uptime":274150},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18374786,"external":22656405,"heapTotal":59211776,"heapUsed":29045104,"rss":153157632},"pid":98895,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:01:03 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[6.525390625,12.74462890625,14.10498046875],"uptime":274157},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18374719,"external":22656338,"heapTotal":59211776,"heapUsed":28659008,"rss":151732224},"pid":99018,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:01:19 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[15.85205078125,14.3720703125,14.65380859375],"uptime":274173},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18375613,"external":22657232,"heapTotal":59998208,"heapUsed":28059944,"rss":149438464},"pid":99257,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:01:31 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[15.2890625,14.33349609375,14.6357421875],"uptime":274185},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18368170,"external":22649789,"heapTotal":59736064,"heapUsed":27923472,"rss":151994368},"pid":99407,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:01:40 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[14.3046875,14.14501953125,14.5673828125],"uptime":274194},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18381944,"external":22663563,"heapTotal":59736064,"heapUsed":28479808,"rss":148865024},"pid":99526,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:01:51 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[12.40283203125,13.7294921875,14.4111328125],"uptime":274205},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18382251,"external":22663870,"heapTotal":59998208,"heapUsed":28995992,"rss":150061056},"pid":99640,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:02:22 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[13.583984375,13.81982421875,14.41552734375],"uptime":274236},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18383146,"external":22664765,"heapTotal":59473920,"heapUsed":28633752,"rss":149471232},"pid":99896,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:02:35 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[12.19677734375,13.51318359375,14.29931640625],"uptime":274249},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18391638,"external":22673257,"heapTotal":59736064,"heapUsed":28958712,"rss":147243008},"pid":231,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:02:49 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[11.45556640625,13.2900390625,14.20556640625],"uptime":274263},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18392936,"external":22674555,"heapTotal":59473920,"heapUsed":29185056,"rss":144310272},"pid":501,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:03:04 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[10.271484375,12.93359375,14.0615234375],"uptime":274278},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18385850,"external":22667469,"heapTotal":59998208,"heapUsed":29020744,"rss":127582208},"pid":676,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:03:19 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[9.06689453125,12.54296875,13.90234375],"uptime":274293},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18386307,"external":22667926,"heapTotal":59998208,"heapUsed":28888672,"rss":131416064},"pid":902,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:03:37 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[7.77587890625,12.02099609375,13.68310546875],"uptime":274311},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18387372,"external":22668991,"heapTotal":59998208,"heapUsed":29077592,"rss":151699456},"pid":1202,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:03:58 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[6.43017578125,11.43603515625,13.43359375],"uptime":274332},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18397294,"external":22678913,"heapTotal":59736064,"heapUsed":29222088,"rss":154419200},"pid":1462,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:04:10 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[7.0234375,11.39013671875,13.39306640625],"uptime":274344},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18406084,"external":22687703,"heapTotal":59211776,"heapUsed":29273528,"rss":147636224},"pid":1586,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:04:22 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[6.77783203125,11.12451171875,13.26318359375],"uptime":274356},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18398601,"external":22680220,"heapTotal":59736064,"heapUsed":29318992,"rss":148226048},"pid":1712,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:04:29 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[6.55517578125,11.005859375,13.20849609375],"uptime":274363},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18367886,"external":22649505,"heapTotal":59736064,"heapUsed":28812280,"rss":151339008},"pid":1843,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:04:37 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[6.0751953125,10.7578125,13.0947265625],"uptime":274371},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18368191,"external":22649810,"heapTotal":59736064,"heapUsed":29121448,"rss":155336704},"pid":1960,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:05:00 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[5.9169921875,10.4296875,12.9228515625],"uptime":274394},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18369464,"external":22651083,"heapTotal":59736064,"heapUsed":28598664,"rss":154091520},"pid":2206,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:06:27 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[26.8271484375,14.9169921875,14.3095703125],"uptime":274481},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18374482,"external":22656101,"heapTotal":59736064,"heapUsed":29034168,"rss":151027712},"pid":3217,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat Jun 21 2025 15:09:30 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[12.505859375,17.2666015625,15.69921875],"uptime":274664},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/workspace/chatWithDb/backend/src/index.ts"],"cwd":"/Users/<USER>/workspace/chatWithDb/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","gid":1431713220,"memoryUsage":{"arrayBuffers":18361534,"external":22643161,"heapTotal":59998208,"heapUsed":28616936,"rss":148340736},"pid":5580,"uid":195831883,"version":"v20.19.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js:635:24)\n    at path (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:78:5)\n    at Object.<anonymous> (/Users/<USER>/workspace/chatWithDb/backend/src/index.ts:94:2)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.transformer (/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2067,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":"path","line":78,"method":null,"native":false},{"column":2,"file":"/Users/<USER>/workspace/chatWithDb/backend/src/index.ts","function":null,"line":94,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":1104,"file":"/Users/<USER>/workspace/chatWithDb/node_modules/tsx/dist/register-D46fvsV_.cjs","function":"Object.transformer","line":3,"method":"transformer","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
