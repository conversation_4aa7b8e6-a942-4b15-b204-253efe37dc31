# Comprehensive Logging Framework

This backend now includes a comprehensive logging framework built with <PERSON> that provides extensive monitoring, debugging, and performance tracking capabilities.

## Features

### 🎯 Core Logging Capabilities
- **Structured JSON logging** for production environments
- **Colored console logging** for development
- **Request correlation IDs** for tracing requests across services
- **Performance monitoring** with execution time tracking
- **Log rotation** with automatic file management
- **Multiple log levels** (error, warn, info, debug)
- **Specialized log types** (database, API, AI operations, security, health)

### 📊 What Gets Logged

#### HTTP Requests
- Request start/end with correlation IDs
- Method, URL, status codes, response times
- Request/response bodies (configurable)
- Headers (with sensitive data redaction)
- Slow request detection (>1000ms by default)

#### Database Operations
- Connection establishment and testing
- Query execution with performance metrics
- Schema retrieval and validation
- Error handling and retry logic

#### AI/LLM Operations
- Model initialization and configuration
- Query generation timing
- Intent classification results
- Response generation performance
- Token usage and model performance

#### System Health
- Service startup and shutdown
- Configuration loading
- Health check results
- Resource usage monitoring

### 📁 Log Files Structure

```
backend/logs/
├── combined-YYYY-MM-DD.log     # All logs (info level and above)
├── error-YYYY-MM-DD.log        # Error logs only
├── performance-YYYY-MM-DD.log  # Performance metrics
├── exceptions.log              # Uncaught exceptions
└── rejections.log              # Unhandled promise rejections
```

### 🔧 Configuration

#### Environment Variables
```bash
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable/disable console logging
ENABLE_CONSOLE_LOGS=true

# Enable/disable file logging
ENABLE_FILE_LOGS=true

# Environment (affects log format)
NODE_ENV=development
```

#### Configuration File
Edit `backend/src/config/logging.json` to customize:
- Log levels and output formats
- File rotation settings
- Sensitive field redaction
- Performance thresholds

### 🚀 Usage Examples

#### Basic Logging
```typescript
import { logger } from './services/Logger';

// Basic log levels
logger.error('Something went wrong', { userId: 123 });
logger.warn('This is a warning', { operation: 'user_update' });
logger.info('Operation completed', { duration: 150 });
logger.debug('Debug information', { details: {...} });
```

#### Specialized Logging
```typescript
// Database operations
logger.database('Query executed', {
  databaseId: 'mongodb',
  duration: 45,
  resultCount: 10
});

// API operations
logger.api('Request processed', {
  method: 'POST',
  url: '/api/chat',
  statusCode: 200,
  duration: 1200
});

// Performance monitoring
logger.performance('Slow operation detected', {
  operation: 'ai_query_generation',
  duration: 5000
});

// Security events
logger.security('Authentication failed', {
  ip: '***********',
  userAgent: 'Mozilla/5.0...'
});
```

#### Request Lifecycle Tracking
```typescript
// Start tracking a request
const requestId = logger.startRequest('POST', '/api/chat');

// Log database operations within the request
const dbOpId = logger.startDatabaseOperation('query', 'mongodb', requestId);
// ... perform database operation
logger.endDatabaseOperation(dbOpId, 'query', 'mongodb', 150, true, requestId);

// End request tracking
logger.endRequest(requestId, 200, 1500, 'POST', '/api/chat');
```

### 📈 Performance Monitoring

The logging framework automatically tracks:
- **Request duration** - Total time from request start to response
- **Database query performance** - Individual query execution times
- **AI operation timing** - LLM query generation and response times
- **Slow request detection** - Automatic flagging of requests >1000ms
- **Resource usage** - Memory and CPU metrics (when available)

### 🔍 Log Analysis

#### Finding Specific Requests
```bash
# Find all logs for a specific request ID
grep "abc123def" backend/logs/combined-*.log

# Find slow requests
grep "slow_request" backend/logs/performance-*.log

# Find database errors
grep "database.*error" backend/logs/error-*.log
```

#### Performance Analysis
```bash
# Find operations taking longer than 1 second
grep '"duration":[0-9][0-9][0-9][0-9]' backend/logs/performance-*.log

# Find AI operations
grep '"service":"ai"' backend/logs/combined-*.log
```

### 🛡️ Security Features

- **Sensitive data redaction** - Passwords, tokens, and API keys are automatically redacted
- **Request sanitization** - Large payloads are truncated with metadata preserved
- **IP tracking** - Client IP addresses logged for security monitoring
- **Authentication logging** - Login attempts and failures tracked

### 🧪 Testing the Logging

Run the comprehensive logging test:
```bash
node backend/test_logging.js
```

This will:
- Test all major endpoints
- Generate various log types
- Demonstrate correlation ID tracking
- Show performance monitoring
- Create sample log files

### 📊 Log Levels Guide

| Level | When to Use | Examples |
|-------|-------------|----------|
| `error` | System errors, exceptions | Database connection failures, API errors |
| `warn` | Warnings, recoverable issues | Slow queries, deprecated API usage |
| `info` | General information | Request completion, service startup |
| `debug` | Detailed debugging info | Variable values, flow control |

### 🔄 Log Rotation

Logs are automatically rotated:
- **Daily rotation** - New files created each day
- **Size limits** - Files split when they exceed 20MB
- **Retention** - Old files automatically compressed and deleted
- **Error logs** - Kept for 14 days
- **Combined logs** - Kept for 7 days
- **Performance logs** - Kept for 3 days

### 🎛️ Runtime Configuration

Change log levels without restarting:
```typescript
import { logger } from './services/Logger';

// Change log level at runtime
logger.setLogLevel('debug');

// Get current log level
const currentLevel = logger.getLogLevel();
```

### 📝 Best Practices

1. **Use correlation IDs** - Always include requestId in related operations
2. **Include context** - Add relevant metadata to log entries
3. **Performance tracking** - Log operation start/end times
4. **Error details** - Include stack traces and error context
5. **Structured data** - Use objects for metadata rather than string interpolation
6. **Appropriate levels** - Use the right log level for each message

### 🔧 Troubleshooting

#### No logs appearing
- Check `LOG_LEVEL` environment variable
- Verify `ENABLE_CONSOLE_LOGS` is true for development
- Ensure logs directory exists and is writable

#### Log files not created
- Check `ENABLE_FILE_LOGS` environment variable
- Verify backend/logs directory permissions
- Check disk space availability

#### Performance impact
- Adjust log level to `warn` or `error` in production
- Disable console logging in production
- Use log sampling for high-traffic applications

This logging framework provides comprehensive visibility into your application's behavior, making debugging, monitoring, and performance optimization much easier.
