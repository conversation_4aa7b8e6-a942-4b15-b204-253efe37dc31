#!/usr/bin/env node

// Test script to demonstrate intent classification functionality
const axios = require('axios');

const API_URL = 'http://localhost:3001/api/chat';

const testQuestions = [
  // General conversation questions (should NOT generate database queries)
  { question: "Hello, how are you?", expectedIntent: "GENERAL" },
  { question: "What can you do?", expectedIntent: "GENERAL" },
  { question: "Tell me a joke", expectedIntent: "GENERAL" },
  { question: "What is this application?", expectedIntent: "GENERAL" },
  { question: "Good morning!", expectedIntent: "GENERAL" },
  
  // Database questions (should generate database queries)
  { question: "How many users do we have?", expectedIntent: "DATABASE" },
  { question: "Show me all users", expectedIntent: "DATABASE" },
  { question: "How many active users are there?", expectedIntent: "DATABASE" },
  { question: "Show me users from New York", expectedIntent: "DATABASE" },
  { question: "Find inactive users", expectedIntent: "DATABASE" },
];

async function testQuestion(question, expectedIntent) {
  try {
    console.log(`\n🔍 Testing: "${question}"`);
    console.log(`   Expected: ${expectedIntent}`);
    
    const response = await axios.post(API_URL, {
      message: question,
      collection: "users",
      database: "test"
    });
    
    const hasQuery = response.data.query !== undefined;
    const actualIntent = hasQuery ? "DATABASE" : "GENERAL";
    
    console.log(`   Actual: ${actualIntent}`);
    console.log(`   Result: ${actualIntent === expectedIntent ? '✅ PASS' : '❌ FAIL'}`);
    
    if (actualIntent === "DATABASE") {
      console.log(`   Query: ${JSON.stringify(response.data.query)}`);
      console.log(`   Results: ${response.data.results ? response.data.results.length + ' records' : 'No results'}`);
    }
    
    console.log(`   Response: "${response.data.response.substring(0, 100)}${response.data.response.length > 100 ? '...' : ''}"`);
    
    return actualIntent === expectedIntent;
  } catch (error) {
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Intent Classification Tests\n');
  console.log('=' .repeat(60));
  
  let passed = 0;
  let total = testQuestions.length;
  
  for (const test of testQuestions) {
    const success = await testQuestion(test.question, test.expectedIntent);
    if (success) passed++;
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log(`📊 Test Results: ${passed}/${total} tests passed`);
  console.log(`Success Rate: ${Math.round((passed/total) * 100)}%`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Intent classification is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please review the implementation.');
  }
}

// Check if axios is available
try {
  require('axios');
  runTests().catch(console.error);
} catch (error) {
  console.log('❌ This test script requires axios. Install it with: npm install axios');
  console.log('Or test manually using curl commands as shown in the documentation.');
}
