export interface ChatMessage {
  id: string;
  message: string;
  timestamp: Date;
  isUser: boolean;
  query?: any;
  results?: any[];
}

export interface ChatRequest {
  message: string;
  database?: string;
  collection?: string;
  table?: string;
  databaseId?: string;
  databaseType?: string;
}

export interface ChatResponse {
  response: string;
  query?: any;
  results?: any[];
  error?: string;
  executionTime?: number;
  databaseType?: string;
  detectionInfo?: string;
  usedDatabase?: string;
  usedTable?: string;
  usedDatabaseId?: string;
}

export interface DatabaseInfo {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  databases?: string[];
  tables?: string[];
  collections?: string[];
}
