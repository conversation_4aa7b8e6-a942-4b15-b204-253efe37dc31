import React, { useState, useEffect, useRef } from 'react';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import DatabaseSelector from './DatabaseSelector';
import { apiService } from '../services/apiService';
import { ChatMessage } from '../types';

export const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    ollama: string;
    databases: Record<string, boolean>;
  } | null>(null);

  // Database selection state
  const [selectedDatabaseId, setSelectedDatabaseId] = useState('mongodb');
  const [selectedDatabase, setSelectedDatabase] = useState('test');
  const [selectedDatabaseType, setSelectedDatabaseType] = useState('mongodb');

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Check connection status on component mount
    checkConnectionStatus();
  }, []);

  const checkConnectionStatus = async () => {
    try {
      const health = await fetch('/api/health').then(res => res.json());
      setConnectionStatus(health.services);
    } catch (error) {
      console.error('Failed to check connection status:', error);
      setConnectionStatus({ ollama: 'disconnected', databases: {} });
    }
  };

  const handleDatabaseChange = (databaseId: string, database: string, databaseType: string) => {
    setSelectedDatabaseId(databaseId);
    setSelectedDatabase(database);
    setSelectedDatabaseType(databaseType);

    // Clear messages when switching databases
    setMessages([]);
  };



  const handleSendMessage = async (messageText: string) => {
    // Collections are always auto-detected, so no need to check for selection

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: messageText,
      timestamp: new Date(),
      isUser: true,
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Add a temporary "thinking" message
    const thinkingMessage: ChatMessage = {
      id: 'thinking-' + Date.now().toString(),
      message: 'AI is processing your question... This may take up to 2 minutes for complex queries.',
      timestamp: new Date(),
      isUser: false,
    };
    setMessages(prev => [...prev, thinkingMessage]);

    try {
      const requestBody = {
        message: messageText,
        databaseId: selectedDatabaseId,
        database: selectedDatabase,
        databaseType: selectedDatabaseType
      };

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      let aiMessageText = data.response;

      // Add detection information if available
      if (data.detectionInfo) {
        aiMessageText += `\n\n🔍 **Detection Info:** ${data.detectionInfo}`;
      }

      // Show what database/table was actually used
      if (data.usedDatabaseId && data.usedDatabase && data.usedTable) {
        aiMessageText += `\n📊 **Used:** ${data.usedDatabaseId}.${data.usedDatabase}.${data.usedTable}`;
      }

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: aiMessageText,
        timestamp: new Date(),
        isUser: false,
        query: data.query,
        results: data.results,
      };

      // Remove thinking message and add AI response
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('thinking-')).concat(aiMessage));
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        isUser: false,
      };

      // Remove thinking message and add error message
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('thinking-')).concat(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  const getDatabaseStatusColor = (status: boolean) => {
    return status ? 'connected' : 'disconnected';
  };

  return (
    <div className="chat-interface">
      <div className="chat-header">
        <h1>Chat with Database</h1>
        <div className="connection-status">
          <div className={`status-indicator ${connectionStatus?.ollama === 'connected' ? 'connected' : 'disconnected'}`}>
            Ollama: {connectionStatus?.ollama || 'checking...'}
          </div>
          {connectionStatus?.databases && Object.entries(connectionStatus.databases).map(([dbId, status]) => (
            <div key={dbId} className={`status-indicator ${getDatabaseStatusColor(status)}`}>
              {dbId}: {status ? 'connected' : 'disconnected'}
            </div>
          ))}
          <button onClick={checkConnectionStatus} className="refresh-status">
            ↻
          </button>
        </div>
      </div>

      <DatabaseSelector
        selectedDatabaseId={selectedDatabaseId}
        selectedDatabase={selectedDatabase}
        onDatabaseChange={handleDatabaseChange}
      />

      <div className="chat-messages">
        <MessageList messages={messages} />
        <div ref={messagesEndRef} />
      </div>

      <div className="chat-input">
        <MessageInput onSendMessage={handleSendMessage} isLoading={isLoading} />
      </div>

      <style>{`
        .chat-interface {
          display: flex;
          flex-direction: column;
          height: 100vh;
          max-width: 1200px;
          margin: 0 auto;
          background: white;
        }

        .chat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 2rem;
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
        }

        .chat-header h1 {
          margin: 0;
          color: #495057;
          font-size: 1.5rem;
        }

        .connection-status {
          display: flex;
          gap: 1rem;
          align-items: center;
        }

        .status-indicator {
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
        }

        .status-indicator.connected {
          background: #d4edda;
          color: #155724;
        }

        .status-indicator.disconnected {
          background: #f8d7da;
          color: #721c24;
        }

        .refresh-status {
          background: none;
          border: 1px solid #ced4da;
          border-radius: 4px;
          padding: 0.25rem 0.5rem;
          cursor: pointer;
          font-size: 1rem;
        }

        .refresh-status:hover {
          background: #e9ecef;
        }

        .chat-messages {
          flex: 1;
          overflow-y: auto;
          padding: 1rem;
        }

        .chat-input {
          padding: 1rem 2rem;
          background: #f8f9fa;
          border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
          .chat-header {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
          }

          .connection-status {
            flex-wrap: wrap;
            justify-content: center;
          }

          .chat-input {
            padding: 1rem;
          }
        }
      `}</style>
    </div>
  );
};
