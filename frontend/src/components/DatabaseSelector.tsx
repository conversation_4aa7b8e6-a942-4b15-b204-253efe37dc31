import React, { useState, useEffect } from 'react';
import { DatabaseInfo } from '../types';

interface DatabaseSelectorProps {
  selectedDatabaseId: string;
  selectedDatabase: string;
  onDatabaseChange: (databaseId: string, database: string, databaseType: string) => void;
}

interface DatabasesResponse {
  databases: DatabaseInfo[];
}

interface TablesResponse {
  databaseId: string;
  database: string;
  databaseType: string;
  tables: string[];
  collections: string[];
}

const DatabaseSelector: React.FC<DatabaseSelectorProps> = ({
  selectedDatabaseId,
  selectedDatabase,
  onDatabaseChange,
}) => {
  const [availableDatabases, setAvailableDatabases] = useState<DatabaseInfo[]>([]);
  const [availableDatabaseNames, setAvailableDatabaseNames] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available database configurations
  useEffect(() => {
    const fetchDatabases = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/databases');
        if (!response.ok) {
          throw new Error('Failed to fetch databases');
        }
        const data: DatabasesResponse = await response.json();
        setAvailableDatabases(data.databases);

        // Set default database names for the selected database config
        const selectedDbConfig = data.databases.find(db => db.id === selectedDatabaseId);
        if (selectedDbConfig) {
          setAvailableDatabaseNames(selectedDbConfig.databases || []);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch databases');
      } finally {
        setLoading(false);
      }
    };

    fetchDatabases();
  }, [selectedDatabaseId]);

  // No need to fetch tables/collections anymore - they will be auto-detected

  const handleDatabaseConfigChange = (databaseId: string) => {
    const dbConfig = availableDatabases.find(db => db.id === databaseId);
    if (dbConfig) {
      setAvailableDatabaseNames(dbConfig.databases || []);
      const firstDatabase = dbConfig.databases?.[0] || 'test';
      onDatabaseChange(databaseId, firstDatabase, dbConfig.type);
    }
  };

  const handleDatabaseNameChange = (databaseName: string) => {
    const dbConfig = availableDatabases.find(db => db.id === selectedDatabaseId);
    if (dbConfig) {
      onDatabaseChange(selectedDatabaseId, databaseName, dbConfig.type);
    }
  };

  if (loading && availableDatabases.length === 0) {
    return <div className="database-selector loading">Loading databases...</div>;
  }

  if (error) {
    return <div className="database-selector error">Error: {error}</div>;
  }

  const selectedDbConfig = availableDatabases.find(db => db.id === selectedDatabaseId);

  return (
    <div className="database-selector">
      <div className="info-section">
        <div className="collection-info">
          🤖 <strong>Collections are automatically detected</strong> from your questions - no manual selection needed!
        </div>
      </div>

      <div className="selector-row">
        <div className="selector-group">
          <label htmlFor="database-config">Database Type:</label>
          <select
            id="database-config"
            value={selectedDatabaseId}
            onChange={(e) => handleDatabaseConfigChange(e.target.value)}
            disabled={loading}
          >
            {availableDatabases.map((db) => (
              <option key={db.id} value={db.id}>
                {db.name} ({db.type.toUpperCase()})
              </option>
            ))}
          </select>
        </div>

        <div className="selector-group">
          <label htmlFor="database-name">Database:</label>
          <select
            id="database-name"
            value={selectedDatabase}
            onChange={(e) => handleDatabaseNameChange(e.target.value)}
            disabled={loading || availableDatabaseNames.length === 0}
          >
            {availableDatabaseNames.map((dbName) => (
              <option key={dbName} value={dbName}>
                {dbName}
              </option>
            ))}
          </select>
        </div>

        <div className="selector-group">
          <label htmlFor="collection-info">Collection/Table:</label>
          <div className="auto-detect-display">
            🤖 Always auto-detected from your question
          </div>
        </div>
      </div>

      {selectedDbConfig && (
        <div className="database-info">
          <span className="database-type-badge" data-type={selectedDbConfig.type}>
            {selectedDbConfig.type.toUpperCase()}
          </span>
          {selectedDatabase && (
            <span className="selected-target">
              {selectedDatabase}.<em>auto-detected</em>
            </span>
          )}
        </div>
      )}

      <style>{`
        .database-selector {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 16px;
        }

        .info-section {
          margin-bottom: 16px;
          padding-bottom: 16px;
          border-bottom: 1px solid #e9ecef;
        }

        .collection-info {
          padding: 12px 16px;
          background: #e8f5e8;
          border: 1px solid #c3e6c3;
          border-radius: 6px;
          font-size: 14px;
          color: #2d5a2d;
          text-align: center;
        }

        .auto-detect-display {
          padding: 8px 12px;
          background: #f0f8ff;
          border: 1px solid #d0e7ff;
          border-radius: 4px;
          font-size: 14px;
          color: #0066cc;
          font-style: italic;
          text-align: center;
        }

        .selector-row {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
          align-items: end;
        }

        .selector-group {
          display: flex;
          flex-direction: column;
          min-width: 150px;
          flex: 1;
        }

        .selector-group label {
          font-size: 12px;
          font-weight: 600;
          color: #495057;
          margin-bottom: 4px;
        }

        .selector-group select {
          padding: 8px 12px;
          border: 1px solid #ced4da;
          border-radius: 4px;
          background: white;
          font-size: 14px;
          color: #495057;
        }

        .selector-group select:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .selector-group select:disabled {
          background: #e9ecef;
          color: #6c757d;
          cursor: not-allowed;
        }

        .database-info {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #e9ecef;
        }

        .database-type-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 11px;
          font-weight: 600;
          text-transform: uppercase;
        }

        .database-type-badge[data-type="mongodb"] {
          background: #4caf50;
          color: white;
        }

        .database-type-badge[data-type="postgresql"] {
          background: #336791;
          color: white;
        }

        .database-type-badge[data-type="mysql"] {
          background: #f29111;
          color: white;
        }

        .selected-target {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          color: #495057;
          background: #e9ecef;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .loading, .error {
          padding: 16px;
          text-align: center;
          border-radius: 8px;
        }

        .loading {
          background: #e3f2fd;
          color: #1976d2;
        }

        .error {
          background: #ffebee;
          color: #c62828;
        }

        @media (max-width: 768px) {
          .selector-row {
            flex-direction: column;
          }

          .selector-group {
            min-width: unset;
          }
        }
      `}</style>
    </div>
  );
};

export default DatabaseSelector;
